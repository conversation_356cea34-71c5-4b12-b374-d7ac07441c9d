/**
 * @file src/renderer/components/FileTree.tsx
 * @description Simplified file tree component for RAG system
 */
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import type { Project, FileTreeNode } from '../../shared/ipc.d';
import '../styles/globals.css';

interface TreeNodeData extends FileTreeNode {
  level: number;
  isExpanded: boolean;
  parentId: string | null;
}

interface FileTreeProps {
  project: Project | null;
  onSelectionChange: (selected: Set<string>) => void;
  compactMode?: 'compact' | 'normal';
  showCheckboxes?: boolean;
}

interface TreeNodeProps {
  node: TreeNodeData;
  style: React.CSSProperties;
  onToggle: (node: TreeNodeData) => void;
  onSelect: (node: TreeNodeData) => void;
  selectionState: 'all' | 'none' | 'some';
  rowIndex: number;
  isActive: boolean;
  loadingNodes: Set<string>;
}

// Simple localStorage hook
function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  const setStoredValue = useCallback(
    (newValue: T | ((prev: T) => T)) => {
      setValue((prev) => {
        const valueToStore = typeof newValue === 'function' ? (newValue as (prev: T) => T)(prev) : newValue;
        try {
          localStorage.setItem(key, JSON.stringify(valueToStore));
        } catch (error) {
          console.warn('Failed to save to localStorage:', error);
        }
        return valueToStore;
      });
    },
    [key]
  );

  return [value, setStoredValue] as const;
}

// Simple SVG icons
const ICONS = {
  folderOpen: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
  folderClosed: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
  code: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>`,
  document: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg>`,
  image: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>`,
  chevron: `<svg class="size-4 transition-transform duration-150 ease-out" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>`,
  spinner: `<svg class="size-4 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`
};

// Smart gitignore patterns for auto-selection - files/folders to EXCLUDE from RAG
const GITIGNORE_PATTERNS = [
  // Dependencies
  'node_modules', '.pnpm-store', 'bower_components', 'vendor', '__pycache__',
  // Build outputs
  'dist', 'build', 'out', '.next', '.nuxt', '.output', 'target', 'bin', 'obj', 'release',
  // Cache directories
  '.cache', '.parcel-cache', '.vite', '.turbo', '.webpack', '.pytest_cache',
  // IDE and editor files
  '.vscode', '.idea', '*.swp', '*.swo', '*~', '.DS_Store', 'Thumbs.db',
  // Logs
  '*.log', 'logs', 'npm-debug.log*', 'yarn-debug.log*', 'yarn-error.log*',
  // Runtime data
  'pids', '*.pid', '*.seed', '*.pid.lock',
  // Coverage directory used by tools like istanbul
  'coverage', '.nyc_output',
  // Dependency directories
  'jspm_packages',
  // Optional npm cache directory
  '.npm',
  // Optional eslint cache
  '.eslintcache',
  // Microbundle cache
  '.rpt2_cache', '.rts2_cache_cjs', '.rts2_cache_es', '.rts2_cache_umd',
  // Optional REPL history
  '.node_repl_history',
  // Output of 'npm pack'
  '*.tgz',
  // Yarn Integrity file
  '.yarn-integrity',
  // dotenv environment variables file
  '.env', '.env.local', '.env.development.local', '.env.test.local', '.env.production.local',
  // Stores VSCode versions used for testing VSCode extensions
  '.vscode-test',
  // Git
  '.git', '.gitignore',
  // OS generated files
  '.DS_Store', '.DS_Store?', '._*', '.Spotlight-V100', '.Trashes', 'ehthumbs.db', 'Thumbs.db',
  // Temporary files
  'tmp', 'temp', '*.tmp', '*.temp',
  // Additional common patterns
  '*.exe', '*.dll', '*.so', '*.dylib', '*.class', '*.jar', '*.war',
  // Lock files (usually not needed for RAG)
  'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml', 'Pipfile.lock', 'poetry.lock'
] as const;

// Check if path should be ignored by default
const shouldIgnoreByDefault = (path: string, name: string): boolean => {
  const lowerName = name.toLowerCase();
  const lowerPath = path.toLowerCase();

  return GITIGNORE_PATTERNS.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(lowerName) || regex.test(lowerPath);
    }
    return lowerName === pattern || lowerPath.includes(`/${pattern}`) || lowerPath.includes(`\\${pattern}`);
  });
};

const getFileIcon = (fileName: string, isDirectory: boolean, isExpanded = false): string => {
  if (isDirectory) {
    return isExpanded ? ICONS.folderOpen : ICONS.folderClosed;
  }

  const ext = fileName.toLowerCase().split('.').pop() || '';

  if (['js', 'ts', 'tsx', 'jsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'dart'].includes(ext)) {
    return ICONS.code;
  } else if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'bmp', 'webp', 'ico', 'tiff', 'avif'].includes(ext)) {
    return ICONS.image;
  } else {
    return ICONS.document;
  }
};

// Optimized color mapping with CSS custom properties
const getFileTypeColor = (fileName: string, isDirectory: boolean): string => {
  if (isDirectory) return 'text-primary-400';

  const ext = fileName.toLowerCase().split('.').pop() || '';

  // Use CSS custom properties for better performance
  switch (ext) {
    case 'js': case 'ts': case 'tsx': case 'jsx': return 'text-yellow-400';
    case 'py': return 'text-blue-400';
    case 'java': case 'kt': case 'scala': return 'text-orange-400';
    case 'cpp': case 'c': case 'cs': return 'text-blue-600';
    case 'css': case 'scss': case 'sass': case 'less': return 'text-blue-500';
    case 'html': case 'htm': return 'text-orange-500';
    case 'json': case 'yaml': case 'yml': case 'toml': return 'text-gray-400';
    case 'md': case 'mdx': return 'text-blue-300';
    case 'png': case 'jpg': case 'jpeg': case 'gif': case 'svg': case 'webp': return 'text-green-400';
    case 'rs': return 'text-orange-600';
    case 'go': return 'text-cyan-400';
    case 'php': return 'text-purple-400';
    case 'rb': return 'text-red-400';
    case 'swift': return 'text-orange-300';
    case 'dart': return 'text-blue-500';
    default: return 'text-gray-500';
  }
};

// Simple TreeNode Component
const TreeNode = React.memo((
  {
    node,
    style,
    onToggle,
    onSelect,
    selectionState,
    rowIndex,
    isActive,
    loadingNodes,
  }: TreeNodeProps
) => {
  const [isHovered, setIsHovered] = useState(false);
  const isDir = !!node.hasChildren;
  const isLoading = loadingNodes.has(node.id);
  const iconSvg = getFileIcon(node.name, isDir, node.isExpanded);
  const colorClass = getFileTypeColor(node.name, isDir);

  const handleToggle = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (isDir && !isLoading) {
        onToggle(node);
      }
    },
    [isDir, isLoading, onToggle, node]
  );

  const handleSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      e.stopPropagation();
      onSelect(node);
    },
    [onSelect, node]
  );

  const paddingLeft = `${8 + node.level * 16}px`;

  const containerClasses = useMemo(() => {
    const base = `flex items-center px-2 py-1 cursor-pointer rounded-sm mx-1 transition-colors`;

    if (isActive) {
      return `${base} bg-primary-500/20 border border-primary-500/50`;
    }
    if (isHovered) {
      return `${base} bg-gray-800/40 hover:bg-gray-800/60 border border-gray-700/30`;
    }
    return `${base} border border-transparent hover:bg-gray-800/30`;
  }, [isActive, isHovered]);

  return (
    <div
      style={style}
      className={`w-full ${isActive ? 'relative z-20' : 'relative z-10'}`}
      data-rowindex={rowIndex}
      tabIndex={0}
      role="treeitem"
      aria-expanded={isDir ? node.isExpanded : undefined}
      aria-level={node.level + 1}
      aria-selected={selectionState === 'all'}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={containerClasses}
        style={{ paddingLeft }}
        onClick={handleToggle}
      >
        {/* Selection Checkbox */}
        <div className="relative group mr-2">
          <input
            type="checkbox"
            checked={selectionState === 'all'}
            ref={(input) => {
              if (input) input.indeterminate = selectionState === 'some';
            }}
            onChange={handleSelect}
            onClick={(e) => e.stopPropagation()}
            className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-2"
            aria-label={`${selectionState === 'all' ? 'Deselect' : 'Select'} ${node.name}`}
          />
        </div>

        {/* Expand/Collapse Icon */}
        {isDir && (
          <div
            className="flex items-center justify-center rounded-sm cursor-pointer size-4 mr-1 hover:bg-gray-700/40 transition-colors"
            onClick={handleToggle}
          >
            {isLoading ? (
              <div dangerouslySetInnerHTML={{ __html: ICONS.spinner }} />
            ) : (
              <div
                className={`text-gray-400 transition-transform duration-150 ease-out ${node.isExpanded ? 'rotate-90' : 'rotate-0'}`}
                dangerouslySetInnerHTML={{ __html: ICONS.chevron }}
              />
            )}
          </div>
        )}

        {/* File/Folder Icon */}
        <div className="flex items-center mr-1.5">
          <div
            className={`${colorClass} ${isActive ? 'brightness-110' : ''}`}
            dangerouslySetInnerHTML={{ __html: iconSvg }}
          />
        </div>

        {/* File/Folder Name */}
        <span
          className={`flex-1 select-none whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium ${isActive ? 'text-primary-300' : 'text-gray-200'}`}
          title={node.name}
        >
          {node.name}
        </span>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.node.id === nextProps.node.id &&
    prevProps.node.isExpanded === nextProps.node.isExpanded &&
    prevProps.selectionState === nextProps.selectionState &&
    prevProps.isActive === nextProps.isActive &&
    prevProps.loadingNodes.has(prevProps.node.id) === nextProps.loadingNodes.has(nextProps.node.id)
  );
});

// Main FileTree Component
export const FileTree = ({
  project,
  onSelectionChange,
  compactMode = 'normal',
  showCheckboxes = true,
}: FileTreeProps) => {
  const [nodes, setNodes] = useState<Record<string, FileTreeNode[]>>({});
  const [loading, setLoading] = useState(false);
  const [loadingNodes, setLoadingNodes] = useState<Set<string>>(new Set());
  const [activeNodeId, setActiveNodeId] = useState<string | null>(null);
  const parentRef = useRef<HTMLDivElement>(null);

  // State management
  const [expanded, setExpanded] = useLocalStorage<string[]>(
    `fileTree-expanded-${project?.path || 'default'}`,
    []
  );
  const [selection, setSelection] = useLocalStorage<Record<string, 'all' | 'none' | 'some'>>(
    `fileTree-selection-${project?.path || 'default'}`,
    {}
  );

  const expandedSet = useMemo(() => new Set(expanded), [expanded]);

  // Node flattening
  const { nodeMap, flatNodes } = useMemo(() => {
    const nodeMap = new Map<string, TreeNodeData>();
    const flatNodes: TreeNodeData[] = [];

    function flatten(parentId: string, level: number) {
      const children = nodes[parentId] || [];
      for (const node of children) {
        const isExpanded = expandedSet.has(node.id);
        const treeNode: TreeNodeData = {
          ...node,
          level,
          isExpanded,
          parentId: parentId === 'root' ? null : parentId,
        };
        nodeMap.set(node.id, treeNode);
        flatNodes.push(treeNode);

        if (isExpanded && node.hasChildren && nodes[node.id]) {
          flatten(node.id, level + 1);
        }
      }
    }

    if (nodes.root) {
      flatten('root', 0);
    }

    return { nodeMap, flatNodes };
  }, [nodes, expandedSet]);

  // Project initialization with auto-selection
  useEffect(() => {
    if (!project?.path) {
      setNodes({});
      setExpanded([]);
      setSelection({});
      setActiveNodeId(null);
      return;
    }

    const currentProjectPath = project.path;
    setLoading(true);

    window.electronAPI
      .readDirectoryLazy(currentProjectPath)
      .then((rootNodes) => {
        if (!rootNodes) return;

        setNodes({ root: rootNodes });

        // Check existing selection
        const storageKey = `fileTree-selection-${currentProjectPath}`;
        let currentSelection: Record<string, 'all' | 'none' | 'some'> = {};
        try {
          const stored = localStorage.getItem(storageKey);
          currentSelection = stored ? JSON.parse(stored) : {};
        } catch {
          currentSelection = {};
        }
        const hasExistingSelection = Object.keys(currentSelection).length > 0;

        // Auto-select if no existing selection
        if (!hasExistingSelection) {
          console.log('[FileTree] Starting auto-selection process...');
          // Use requestAnimationFrame instead of setTimeout for better performance
          requestAnimationFrame(async () => {
            try {
              console.log('[FileTree] Calling autoSelectFilesRecursively...');
              const initialSelection = await autoSelectFilesRecursively('root');
              console.log('[FileTree] Auto-selection result:', Object.keys(initialSelection).length, 'items');
              console.log('[FileTree] Selected files:', Object.entries(initialSelection).filter(([_, state]) => state === 'all').map(([id]) => id.split(/[/\\]/).pop()));
              setSelection(initialSelection);
            } catch (error) {
              console.warn('Failed to auto-select files:', error);
              // Fallback selection
              const fallbackSelection: Record<string, 'all' | 'none' | 'some'> = {};
              rootNodes.forEach(node => {
                const shouldIgnore = shouldIgnoreByDefault(node.id, node.name);
                fallbackSelection[node.id] = shouldIgnore ? 'none' : 'all';
              });
              console.log('[FileTree] Using fallback selection:', Object.keys(fallbackSelection).length, 'items');
              setSelection(fallbackSelection);
            }
          });
        } else {
          console.log('[FileTree] Using existing selection from localStorage');
          setSelection(currentSelection);
        }
      })
      .catch((error) => {
        console.error('Failed to load directory:', error);
        setNodes({});
      })
      .finally(() => setLoading(false));
  }, [project?.path]); // Remove setExpanded and setSelection from dependencies

  // Selection tracking
  const selectedFiles = useMemo(() => {
    const files = new Set<string>();
    for (const [id, state] of Object.entries(selection)) {
      if (state === 'all') {
        const node = nodeMap.get(id);
        if (node && !node.hasChildren) {
          files.add(id);
        }
      }
    }
    return files;
  }, [selection, nodeMap]);

  useEffect(() => {
    onSelectionChange(selectedFiles);
  }, [selectedFiles, onSelectionChange]);

  // Optimized node toggle with loading states
  const toggleNode = useCallback(
    async (node: TreeNodeData) => {
      if (!node.hasChildren) return;

      const isCurrentlyExpanded = expandedSet.has(node.id);
      
      // Update expanded state immediately for responsive UI
      setExpanded((prev) => {
        const newExpanded = prev.filter((id) => id !== node.id);
        if (!isCurrentlyExpanded) {
          newExpanded.push(node.id);
        }
        return newExpanded;
      });

      // Load children if not already loaded and expanding
      if (!isCurrentlyExpanded && !nodes[node.id]) {
        setLoadingNodes((prev) => new Set([...prev, node.id]));
        
        try {
          const childNodes = await window.electronAPI.readDirectoryLazy(node.id);
          if (childNodes) {
            setNodes((prev) => ({ ...prev, [node.id]: childNodes }));
          }
        } catch (error) {
          console.error('Failed to load directory children:', error);
          // Revert expansion on error
          setExpanded((prev) => prev.filter((id) => id !== node.id));
        } finally {
          setLoadingNodes((prev) => {
            const newSet = new Set(prev);
            newSet.delete(node.id);
            return newSet;
          });
        }
      }
    },
    [expandedSet, nodes, setExpanded]
  );

  // Enhanced selection logic - SELECT ALL by default except gitignore patterns
  const autoSelectFilesRecursively = useCallback(async (nodeId: string): Promise<Record<string, 'all' | 'none' | 'some'>> => {
    const selection: Record<string, 'all' | 'none' | 'some'> = {};
    const loadedNodes: Record<string, FileTreeNode[]> = { ...nodes };

    // Recursive function to process all directories and files
    const processNode = async (currentNodeId: string): Promise<void> => {
      let children = loadedNodes[currentNodeId] || [];

      // If this is a directory that hasn't been loaded yet, load it
      if (currentNodeId !== 'root' && children.length === 0) {
        try {
          const dirChildren = await window.electronAPI.readDirectoryLazy(currentNodeId);
          if (dirChildren && dirChildren.length > 0) {
            // Store in local copy and update state
            loadedNodes[currentNodeId] = dirChildren;
            setNodes(prev => ({ ...prev, [currentNodeId]: dirChildren }));
            children = dirChildren;
          }
        } catch (error) {
          console.warn('Failed to load directory:', currentNodeId, error);
          return;
        }
      }

      // Process children
      await processChildren(currentNodeId, children);
    };

    const processChildren = async (_parentId: string, children: FileTreeNode[]): Promise<void> => {
      console.log('[FileTree] Processing', children.length, 'children in', _parentId);
      for (const child of children) {
        const shouldIgnore = shouldIgnoreByDefault(child.id, child.name);

        if (!child.hasChildren) {
          // For files: SELECT by default, IGNORE only if in gitignore patterns
          selection[child.id] = shouldIgnore ? 'none' : 'all';
          console.log('[FileTree] File:', child.name, shouldIgnore ? 'IGNORED' : 'SELECTED');
        } else {
          // For directories: first set the directory state
          selection[child.id] = shouldIgnore ? 'none' : 'all';
          console.log('[FileTree] Directory:', child.name, shouldIgnore ? 'IGNORED' : 'SELECTED');

          // If directory should not be ignored, process its contents recursively
          if (!shouldIgnore) {
            await processNode(child.id);
          }
        }
      }
    };

    // Start processing from the given node
    await processNode(nodeId);

    console.log('[FileTree] Auto-selection completed for', nodeId, '- processed', Object.keys(selection).length, 'items');
    return selection;
  }, [nodes]);

  const handleSelect = useCallback(
    async (node: TreeNodeData) => {
      const currentState = selection[node.id] || 'none';
      const newState = currentState === 'all' ? 'none' : 'all';

      // If selecting a directory, load its children first if not already loaded
      if (node.hasChildren && newState === 'all') {
        const children = nodes[node.id] || [];
        if (children.length === 0) {
          try {
            setLoadingNodes(prev => new Set([...prev, node.id]));
            const childNodes = await window.electronAPI.readDirectoryLazy(node.id);
            if (childNodes) {
              setNodes(prev => ({ ...prev, [node.id]: childNodes }));
            }
          } catch (error) {
            console.error('Failed to load directory contents:', error);
          } finally {
            setLoadingNodes(prev => {
              const next = new Set(prev);
              next.delete(node.id);
              return next;
            });
          }
        }
      }

      setSelection((prev) => {
        const next = { ...prev };

        // Apply to node and all descendants recursively
        const applyToDescendants = async (nodeId: string, state: 'all' | 'none') => {
          next[nodeId] = state;
          const children = nodes[nodeId] || [];

          // For directories being selected, apply to all children
          for (const child of children) {
            // Skip files/folders that should be ignored by default when selecting
            if (state === 'all' && shouldIgnoreByDefault(child.id, child.name)) {
              next[child.id] = 'none';
            } else {
              applyToDescendants(child.id, state);
            }
          }
        };

        applyToDescendants(node.id, newState);

        // Propagate up to ancestors
        let current = node.parentId ? nodeMap.get(node.parentId) : null;
        while (current) {
          const siblings = nodes[current.parentId || 'root'] || [];
          const siblingStates = siblings.map((s) => next[s.id] || 'none');

          if (siblingStates.every((s) => s === 'all')) {
            next[current.id] = 'all';
          } else if (siblingStates.every((s) => s === 'none')) {
            next[current.id] = 'none';
          } else {
            next[current.id] = 'some';
          }

          current = current.parentId ? nodeMap.get(current.parentId) : null;
        }

        return next;
      });
    },
    [nodes, nodeMap, selection, setSelection, setNodes, setLoadingNodes]
  );

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (!flatNodes.length) return;

      const activeElement = document.activeElement as HTMLElement;
      const currentIndex = activeElement?.dataset?.rowindex
        ? Number(activeElement.dataset.rowindex)
        : 0;

      let targetIndex = currentIndex;
      let handled = true;

      switch (e.key) {
        case 'ArrowDown':
          targetIndex = Math.min(currentIndex + 1, flatNodes.length - 1);
          break;
        case 'ArrowUp':
          targetIndex = Math.max(currentIndex - 1, 0);
          break;
        case 'ArrowRight': {
          const node = flatNodes[currentIndex];
          if (node?.hasChildren && !node.isExpanded) {
            toggleNode(node);
          } else {
            targetIndex = Math.min(currentIndex + 1, flatNodes.length - 1);
          }
          break;
        }
        case 'ArrowLeft': {
          const node = flatNodes[currentIndex];
          if (node?.hasChildren && node.isExpanded) {
            toggleNode(node);
          } else if (node?.parentId) {
            // Navigate to parent
            const parentIndex = flatNodes.findIndex((n: TreeNodeData) => n.id === node.parentId);
            if (parentIndex >= 0) targetIndex = parentIndex;
          }
          break;
        }
        case ' ':
        case 'Enter':
          e.preventDefault();
          const node = flatNodes[currentIndex];
          if (node) {
            if (e.key === ' ') {
              handleSelect(node);
            } else {
              toggleNode(node);
            }
          }
          break;
        case 'Home':
          targetIndex = 0;
          break;
        case 'End':
          targetIndex = flatNodes.length - 1;
          break;
        default:
          handled = false;
      }

      if (handled) {
        e.preventDefault();
        if (targetIndex !== currentIndex) {
          const targetElement = parentRef.current?.querySelector(
            `[data-rowindex="${targetIndex}"]`
          ) as HTMLElement;
          targetElement?.focus();
          setActiveNodeId(flatNodes[targetIndex]?.id || null);
        }
      }
    },
    [flatNodes, toggleNode, handleSelect]
  );

  // Virtualization
  const rowHeight = 32;

  const rowVirtualizer = useVirtualizer({
    count: flatNodes.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 8,
  });

  // Loading state
  if (loading) {
    return (
      <div className="p-4">
        <div className="flex items-center mb-4">
          <div className="size-4 mr-2" dangerouslySetInnerHTML={{ __html: ICONS.spinner }} />
          <span className="text-sm text-gray-400 font-medium">Loading project files...</span>
        </div>
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="h-6 bg-gray-800/40 rounded-sm mb-1 animate-pulse"
            style={{ width: `${100 - i * 3}%` }}
          />
        ))}
      </div>
    );
  }

  // Empty states
  if (!project) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center">
        <div className="size-12 mb-4 text-gray-600" dangerouslySetInnerHTML={{ __html: ICONS.folderClosed.replace('size-4', 'size-12') }} />
        <h3 className="text-lg font-semibold text-gray-400 mb-2">No Project Selected</h3>
        <p className="text-sm text-gray-500">Choose a project to explore its file structure</p>
      </div>
    );
  }

  if (!flatNodes.length && !loading) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center">
        <div className="size-12 mb-4 text-gray-600" dangerouslySetInnerHTML={{ __html: ICONS.folderOpen.replace('size-4', 'size-12') }} />
        <h3 className="text-lg font-semibold text-gray-400 mb-2">Empty Project</h3>
        <p className="text-sm text-gray-500">This project contains no files</p>
      </div>
    );
  }

  // Main tree view
  return (
    <div
      ref={parentRef}
      className="h-full w-full overflow-y-auto overflow-x-hidden"
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="tree"
      aria-label="File tree"
    >
      <div
        className="relative w-full"
        style={{ height: `${rowVirtualizer.getTotalSize()}px` }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualItem) => {
          const node = flatNodes[virtualItem.index];
          if (!node) return null;

          const isActive = activeNodeId === node.id;

          return (
            <TreeNode
              key={`${node.id}-${virtualItem.index}`}
              rowIndex={virtualItem.index}
              node={node}
              onToggle={toggleNode}
              onSelect={handleSelect}
              selectionState={selection[node.id] || 'none'}
              isActive={isActive}
              loadingNodes={loadingNodes}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translate3d(0, ${virtualItem.start}px, 0)`,
              }}
            />
          );
        })}
      </div>
    </div>
  );
};
