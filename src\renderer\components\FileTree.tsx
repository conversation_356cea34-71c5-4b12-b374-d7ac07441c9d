import React, { useState, useEffect, useCallback } from 'react';
import type { Project, FileTreeNode } from '../../shared/ipc.d';

interface FileTreeProps {
  project: Project | null;
  onSelectionChange: (selected: Set<string>) => void;
}

const GITIGNORE_PATTERNS = [
  'node_modules', 'dist', 'build', '.git', '.vscode', '__pycache__', 'target', 'bin', 'obj'
];

const shouldIgnore = (name: string): boolean => {
  return GITIGNORE_PATTERNS.some(pattern => name.toLowerCase().includes(pattern));
};

interface TreeItem extends FileTreeNode {
  level: number;
  expanded: boolean;
}

const TreeNode: React.FC<{
  item: TreeItem;
  selected: boolean;
  onToggle: () => void;
  onSelect: () => void;
}> = ({ item, selected, onToggle, onSelect }) => {
  const isDir = item.hasChildren;
  const paddingLeft = item.level * 20 + 8;

  return (
    <div
      className="flex items-center py-1 px-2 hover:bg-gray-800 cursor-pointer"
      style={{ paddingLeft }}
    >
      <input
        type="checkbox"
        checked={selected}
        onChange={onSelect}
        className="mr-2"
      />

      {isDir && (
        <button onClick={onToggle} className="mr-1 text-gray-400">
          {item.expanded ? '▼' : '▶'}
        </button>
      )}

      <span className="text-gray-200">{item.name}</span>
    </div>
  );
};

export const FileTree = ({ project, onSelectionChange }: FileTreeProps) => {
  const [nodes, setNodes] = useState<Record<string, FileTreeNode[]>>({});
  const [expanded, setExpanded] = useState<Set<string>>(new Set());
  const [selected, setSelected] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  // Flatten tree for rendering
  const flatItems: TreeItem[] = [];

  const flatten = (parentId: string, level: number) => {
    const children = nodes[parentId] || [];
    for (const node of children) {
      const item: TreeItem = {
        ...node,
        level,
        expanded: expanded.has(node.id)
      };
      flatItems.push(item);

      if (item.expanded && item.hasChildren) {
        flatten(node.id, level + 1);
      }
    }
  };

  if (nodes.root) {
    flatten('root', 0);
  }

  // Load project files
  useEffect(() => {
    if (!project?.path) {
      setNodes({});
      setExpanded(new Set());
      setSelected(new Set());
      return;
    }

    setLoading(true);
    window.electronAPI
      .readDirectoryLazy(project.path)
      .then((rootNodes) => {
        if (rootNodes) {
          setNodes({ root: rootNodes });
          // Auto-select files that are not ignored
          const autoSelected = new Set<string>();
          rootNodes.forEach(node => {
            if (!node.hasChildren && !shouldIgnore(node.name)) {
              autoSelected.add(node.id);
            }
          });
          setSelected(autoSelected);
        }
      })
      .catch(console.error)
      .finally(() => setLoading(false));
  }, [project?.path]);

  // Update selection callback
  useEffect(() => {
    onSelectionChange(selected);
  }, [selected, onSelectionChange]);

  // Toggle folder expansion
  const toggleExpanded = useCallback(async (nodeId: string) => {
    const newExpanded = new Set(expanded);

    if (expanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);

      // Load children if not loaded
      if (!nodes[nodeId]) {
        try {
          const children = await window.electronAPI.readDirectoryLazy(nodeId);
          if (children) {
            setNodes(prev => ({ ...prev, [nodeId]: children }));
          }
        } catch (error) {
          console.error('Failed to load children:', error);
          return;
        }
      }
    }

    setExpanded(newExpanded);
  }, [expanded, nodes]);

  // Toggle file selection
  const toggleSelected = useCallback((nodeId: string) => {
    const newSelected = new Set(selected);
    if (selected.has(nodeId)) {
      newSelected.delete(nodeId);
    } else {
      newSelected.add(nodeId);
    }
    setSelected(newSelected);
  }, [selected]);

  // Control buttons
  const handleAutoSelect = useCallback(async () => {
    if (!project?.path) return;

    const autoSelected = new Set<string>();

    const processNodes = (nodeId: string) => {
      const children = nodes[nodeId] || [];
      for (const child of children) {
        if (!child.hasChildren && !shouldIgnore(child.name)) {
          autoSelected.add(child.id);
        } else if (child.hasChildren && !shouldIgnore(child.name)) {
          processNodes(child.id);
        }
      }
    };

    processNodes('root');
    setSelected(autoSelected);
  }, [project?.path, nodes]);

  const handleSelectAll = useCallback(() => {
    const allFiles = new Set<string>();

    const processNodes = (nodeId: string) => {
      const children = nodes[nodeId] || [];
      for (const child of children) {
        if (!child.hasChildren) {
          allFiles.add(child.id);
        } else if (child.hasChildren) {
          processNodes(child.id);
        }
      }
    };

    processNodes('root');
    setSelected(allFiles);
  }, [nodes]);

  const handleUnselectAll = useCallback(() => {
    setSelected(new Set());
  }, []);

  if (loading) {
    return (
      <div className="p-4">
        <div className="text-gray-400">Loading...</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-4">
        <div className="text-gray-400">No project selected</div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Control buttons */}
      <div className="p-2 border-b border-gray-700">
        <div className="flex gap-1">
          <button
            onClick={handleAutoSelect}
            className="flex-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 rounded"
          >
            Auto Select
          </button>
          <button
            onClick={handleSelectAll}
            className="flex-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 rounded"
          >
            Select All
          </button>
          <button
            onClick={handleUnselectAll}
            className="flex-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 rounded"
          >
            Unselect All
          </button>
        </div>
      </div>

      {/* File tree */}
      <div className="flex-1 overflow-y-auto">
        {flatItems.map((item) => (
          <TreeNode
            key={item.id}
            item={item}
            selected={selected.has(item.id)}
            onToggle={() => toggleExpanded(item.id)}
            onSelect={() => toggleSelected(item.id)}
          />
        ))}
      </div>
    </div>
  );
};
