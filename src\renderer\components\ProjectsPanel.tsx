/**
 * @file src/renderer/components/ProjectsPanel.tsx
 * @description Projects panel component for managing and selecting projects
 */

import React from 'react';
import { Box, Typography } from '@mui/material';
import { ProjectExplorer } from './ProjectExplorer';
import { CompactProjectExplorer } from './CompactProjectExplorer';

interface ProjectsPanelProps {
  onProjectSelect: (project: any) => void;
  openProjects?: any[]; // Список уже открытых проектов
  disableAutoSelection?: boolean;
  compact?: boolean; // Использовать компактную версию
}

export const ProjectsPanel: React.FC<ProjectsPanelProps> = ({
  onProjectSelect,
  openProjects = [],
  disableAutoSelection = false,
  compact = false
}) => {
  const handleProjectSelect = (project: any) => {
    console.log('ProjectsPanel.handleProjectSelect called with:', project);
    onProjectSelect(project);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {compact ? (
          <CompactProjectExplorer
            onProjectSelect={handleProjectSelect}
            openProjects={openProjects}
            disableAutoSelection={disableAutoSelection}
          />
        ) : (
          <ProjectExplorer
            onProjectSelect={handleProjectSelect}
            openProjects={openProjects}
            disableAutoSelection={disableAutoSelection}
          />
        )}
      </Box>
    </Box>
  );
};
