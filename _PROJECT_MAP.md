# Metacharts Project Map (High-Level Index)
# Auto-generated: 2025-07-28T00:22:17.416Z
# Purpose: Provides a high-level overview for AI navigation and developer onboarding.
# Stats: 25 files, ~4k lines, ~141k chars, ~~35k tokens

> Legend: An ellipsis (…) at the end of a description means it was truncated. Read the file for full details.

## `./`
- `package.json`: No description found.
- `tsconfig.json`: No description found.

## `src/main/`
- `main.ts`: src/main/main.ts

## `src/main/services/`
- `indexer.worker.ts`: src/main/services/indexer.worker.ts
- `lancedb.ts`: src/main/services/lancedb.ts
- `llm.ts`: src/main/services/llm.ts
- `mcp_api.ts`: src/main/services/mcp_api.ts
- `sqlite.ts`: src/main/services/sqlite.ts

## `src/main/utils/`
- `environment.ts`: No description found.

## `src/renderer/`
- `App.tsx`: src/renderer/App.tsx
- `main.tsx`: src/renderer/main.tsx
- `preload.ts`: src/renderer/preload.ts
- `TestApp.tsx`: src/renderer/TestApp.tsx

## `src/renderer/components/`
- `ChatPanel.tsx`: src/renderer/components/ChatPanel.tsx
- `CompactProjectExplorer.tsx`: src/renderer/components/CompactProjectExplorer.tsx
- `DashboardPanel.tsx`: src/renderer/components/DashboardPanel.tsx
- `EmptyTabContent.tsx`: src/renderer/components/EmptyTabContent.tsx
- `FileExplorerPanel.tsx`: src/renderer/components/FileExplorerPanel.tsx
- `FileTree.tsx`: src/renderer/components/FileTree.tsx
- `ProjectExplorer.tsx`: src/renderer/components/ProjectExplorer.tsx
- `ProjectsPanel.tsx`: src/renderer/components/ProjectsPanel.tsx
- `ProjectTabBar.tsx`: src/renderer/components/ProjectTabBar.tsx
- `QueryInterface.tsx`: src/renderer/components/QueryInterface.tsx
- `SettingsPanel.tsx`: src/renderer/components/SettingsPanel.tsx
- `Sidebar.tsx`: src/renderer/components/Sidebar.tsx