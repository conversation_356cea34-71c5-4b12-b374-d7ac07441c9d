/**
 * @file src/renderer/components/ProjectExplorer.tsx
 * @description Ultra-optimized project manager with Tailwind v4, GPU acceleration, and smooth animations
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { Project } from '../../shared/ipc.d';
import '../styles/globals.css';

// Optimized SVG icons for maximum performance
const ICONS = {
  add: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>`,
  delete: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>`,
  folder: `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
  folderOpen: `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
  storage: `<svg class="size-12" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z"></path></svg>`,
} as const;

interface ProjectExplorerProps {
  onProjectSelect: (project: Project | null) => void;
  openProjects?: Project[];
}

export const ProjectExplorer = ({ onProjectSelect, openProjects = [] }: ProjectExplorerProps) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [hoveredProjectId, setHoveredProjectId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasManualSelection, setHasManualSelection] = useState(false);

  // Optimized project fetching with error handling
  const fetchProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      const projs = await window.electronAPI.getAllProjects();
      setProjects(projs);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Separate effect for initial project selection
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // Handle initial project selection separately
  useEffect(() => {
    if (projects.length > 0 && selectedProjectId === null && !hasManualSelection) {
      const firstProject = projects[0];
      setSelectedProjectId(firstProject.id);
      onProjectSelect(firstProject);
    } else if (projects.length === 0 && selectedProjectId !== null) {
      setSelectedProjectId(null);
      onProjectSelect(null);
      setHasManualSelection(false);
    }
  }, [projects, selectedProjectId, onProjectSelect, hasManualSelection]);

  // Optimized event handlers with loading states
  const handleAddProject = useCallback(async () => {
    try {
      setIsLoading(true);
      const directoryPath = await window.electronAPI.openDirectoryDialog();
      if (directoryPath) {
        const newProject = await window.electronAPI.addProject(directoryPath);
        await fetchProjects();

        // Автоматически выбираем новый добавленный проект
        if (newProject) {
          setSelectedProjectId(newProject.id);
          setHasManualSelection(true);
          onProjectSelect(newProject);
        }
      }
    } catch (error) {
      console.error('Failed to add project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects, onProjectSelect]);

  const handleDeleteProject = useCallback(async (projectId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      setIsLoading(true);
      await window.electronAPI.deleteProject(projectId);
      await fetchProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects]);

  const handleSelectProject = useCallback((project: Project) => {
    setSelectedProjectId(project.id);
    setHasManualSelection(true);
    onProjectSelect(project);
  }, [onProjectSelect]);

  // Memoized project list for better performance
  const projectList = useMemo(() => projects, [projects]);

  return (
    <div className="p-4 h-full flex flex-col gpu-layer">
      {/* Add button */}
      <div className="flex justify-end items-center mb-6 pb-3 border-b border-primary-500/20">
        <button
          onClick={handleAddProject}
          disabled={isLoading}
          className="
            group relative flex items-center gap-2 px-4 py-2
            bg-gradient-to-r from-primary-600 to-primary-500
            hover:from-primary-700 hover:to-primary-600
            disabled:from-gray-600 disabled:to-gray-500
            text-white text-sm font-medium rounded-lg
            transition-all duration-200 ease-out
            shadow-lg shadow-primary-500/25 hover:shadow-primary-500/40
            transform hover:scale-105 active:scale-95
            focus-ring gpu-layer
          "
          title="Add new project"
        >
          <div
            className={`transition-transform duration-200 ${isLoading ? 'animate-spin' : 'group-hover:scale-110'}`}
            dangerouslySetInnerHTML={{ __html: ICONS.add }}
          />
          <span>Add</span>

          {/* Subtle glow effect */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary-600 to-primary-500 opacity-0 group-hover:opacity-20 transition-opacity duration-200 blur-sm -z-10" />
        </button>
      </div>

      {/* Project list with optimized scrolling */}
      <div className="flex-1 overflow-y-auto scrollbar-ultra-smooth">
        {projectList.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8 animate-fade-in gpu-layer">
            <div
              className="mb-4 text-gray-600 opacity-60"
              dangerouslySetInnerHTML={{ __html: ICONS.storage }}
            />
            <h3 className="text-base font-medium text-gray-400 mb-1">
              No projects yet
            </h3>
            <p className="text-sm text-gray-500">
              Click "Add" to get started
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {projectList.map((project, index) => (
              <div
                key={project.id}
                className="
                  group relative animate-slide-in gpu-layer
                  transition-all duration-200 ease-out
                  hover:transform hover:scale-[1.02] hover:-translate-y-0.5
                "
                style={{ animationDelay: `${index * 50}ms` }}
                onMouseEnter={() => setHoveredProjectId(project.id)}
                onMouseLeave={() => setHoveredProjectId(null)}
              >
                <button
                  onClick={() => handleSelectProject(project)}
                  className={`
                    w-full text-left p-4 rounded-xl border transition-all duration-200 ease-out
                    backdrop-blur-sm focus-ring gpu-layer relative overflow-hidden
                    ${selectedProjectId === project.id
                      ? 'bg-gradient-to-br from-primary-500/20 to-primary-600/10 border-primary-500/40 shadow-lg shadow-primary-500/20'
                      : 'bg-gray-800/40 border-gray-700/30 hover:bg-gray-800/60 hover:border-gray-600/50'
                    }
                  `}
                >
                  <div className="flex items-center gap-3 relative z-10">
                    {/* Project Icon */}
                    <div
                      className={`
                        transition-all duration-200 ease-out
                        ${selectedProjectId === project.id ? 'text-primary-300 scale-110' : 'text-gray-400'}
                      `}
                      dangerouslySetInnerHTML={{
                        __html: selectedProjectId === project.id ? ICONS.folderOpen : ICONS.folder
                      }}
                    />

                    {/* Project Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className={`
                        text-sm font-medium mb-1 transition-colors duration-200
                        ${selectedProjectId === project.id ? 'text-primary-200' : 'text-gray-200'}
                      `}>
                        {project.name}
                      </h3>

                      <p className="text-xs text-gray-500 font-mono truncate mb-2">
                        {project.path}
                      </p>

                      <div className="flex items-center gap-2">
                        {(() => {
                          const isOpen = openProjects.some(openProject => openProject?.path === project.path);
                          return (
                            <span className={`
                              inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                              transition-colors duration-200
                              ${isOpen
                                ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                                : selectedProjectId === project.id
                                  ? 'bg-primary-500/20 text-primary-300 border border-primary-500/30'
                                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'
                              }
                            `}>
                              {isOpen ? 'Open' : 'Ready'}
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>

                  {/* Subtle background glow for selected project */}
                  {selectedProjectId === project.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-primary-600/5 rounded-xl opacity-50" />
                  )}
                </button>
                {/* Delete button with smooth animations - исправлен размер */}
                <button
                  onClick={(e) => handleDeleteProject(project.id, e)}
                  disabled={isLoading}
                  className={`
                    absolute top-2 right-2 p-1.5 rounded-md
                    transition-all duration-200 ease-out z-20
                    hover:bg-red-500/20 hover:scale-110 active:scale-95
                    focus-ring disabled:opacity-50 disabled:cursor-not-allowed
                    ${hoveredProjectId === project.id
                      ? 'opacity-100 text-red-400'
                      : 'opacity-0 text-transparent pointer-events-none'
                    }
                  `}
                  title="Delete project"
                >
                  <div dangerouslySetInnerHTML={{ __html: ICONS.delete }} />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
