/**
 * @file src/renderer/App.tsx
 * @description Root React component for the application with tabbed project interface.
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Box, CssBaseline, ThemeProvider, createTheme, Typography, alpha, Button } from '@mui/material';
import { Add, FolderOpen } from '@mui/icons-material';

import { ProjectTabBar } from './components/ProjectTabBar';
import { Sidebar } from './components/Sidebar';
import { ProjectsPanel } from './components/ProjectsPanel';
import { FileExplorerPanel } from './components/FileExplorerPanel';
import { DashboardPanel } from './components/DashboardPanel';
import { ChatPanel } from './components/ChatPanel';
import { SettingsPanel } from './components/SettingsPanel';
import { EmptyTabContent } from './components/EmptyTabContent';
import type { Project } from '../shared/ipc.d.ts';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#9333ea',
      light: '#a855f7',
      dark: '#7c3aed',
    },
    secondary: {
      main: '#c084fc',
      light: '#d8b4fe',
      dark: '#a855f7',
    },
    background: {
      default: '#0f0f0f',
      paper: '#1a1a1a',
    },
    text: {
      primary: '#f1f5f9',
      secondary: '#94a3b8',
      disabled: '#64748b',
    },
    divider: '#2a2a2a',
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h4: {
      fontWeight: 500,
      fontSize: '1.25rem',
    },
    body1: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.75rem',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
          fontFeatureSettings: '"cv02","cv03","cv04","cv11"',
        },
        '*::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '*::-webkit-scrollbar-track': {
          background: '#1a1a1a',
        },
        '*::-webkit-scrollbar-thumb': {
          background: '#9333ea',
          borderRadius: '4px',
          '&:hover': {
            background: '#a855f7',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          borderRadius: 8,
          padding: '8px 16px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(147, 51, 234, 0.3)',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #9333ea 0%, #a855f7 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #7c3aed 0%, #9333ea 100%)',
          },
        },
        outlined: {
          borderColor: '#9333ea',
          color: '#d8b4fe',
          '&:hover': {
            borderColor: '#a855f7',
            backgroundColor: alpha('#9333ea', 0.1),
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#1a1a1a',
          border: '1px solid #2a2a2a',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: 48,
          backgroundColor: '#1a1a1a',
          borderBottom: '1px solid #2a2a2a',
        },
        indicator: {
          background: 'linear-gradient(90deg, #9333ea 0%, #a855f7 100%)',
          height: 3,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          fontSize: '0.875rem',
          color: '#94a3b8',
          minHeight: 48,
          padding: '12px 16px',
          '&.Mui-selected': {
            color: '#d8b4fe',
          },
          '&:hover': {
            color: '#c084fc',
            backgroundColor: alpha('#9333ea', 0.1),
          },
        },
      },
    },
  },
});



// Define the project tab interface
interface ProjectTab {
  id: string;
  project: Project | null;
  name: string;
  isActive: boolean;
  selectedFiles: Set<string>;
}

// Define sidebar section types
type SidebarSection = 'files' | 'dashboard' | 'chat' | 'settings';

function App() {
  // Tab management state
  const [tabs, setTabs] = useState<ProjectTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [sidebarSection, setSidebarSection] = useState<SidebarSection>('files');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Get the currently active tab
  const activeTab = tabs.find(tab => tab.id === activeTabId) || null;

  // Handle file selection change
  const handleFileSelectionChange = useCallback((selection: string[]) => {
    if (activeTabId) {
      setTabs(prevTabs =>
        prevTabs.map(tab =>
          tab.id === activeTabId
            ? { ...tab, selectedFiles: new Set(selection) }
            : tab
        )
      );
    }
  }, [activeTabId]);

  // Render the right panel based on selected sidebar section
  const renderRightPanel = () => {
    // Если активный таб не имеет проекта, показываем интерфейс выбора проекта
    if (activeTab && !activeTab.project) {
      return (
        <EmptyTabContent
          onProjectSelect={handleProjectSelectForActiveTab}
          openProjects={tabs.map(tab => tab.project)}
        />
      );
    }

    switch (sidebarSection) {
      case 'files':
        return activeTab?.project ? (
          <FileExplorerPanel
            project={activeTab.project}
            onSelectionChange={handleFileSelectionChange}
          />
        ) : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <FolderOpen sx={{ fontSize: 64, mb: 2, color: '#374151' }} />
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'dashboard':
        return activeTab?.project ? <DashboardPanel project={activeTab.project} /> : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'chat':
        return activeTab?.project ? <ChatPanel project={activeTab.project} /> : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'settings':
        return <SettingsPanel />;
      default:
        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            p: 3
          }}>
            <Typography variant="h6">
              Welcome to SmartRAG
            </Typography>
          </Box>
        );
    }
  };

  // Tab persistence functions
  const saveTabsToStorage = useCallback((tabsToSave: ProjectTab[], activeId: string | null) => {
    try {
      const tabsData = {
        tabs: tabsToSave.map(tab => ({
          id: tab.id,
          project: tab.project ? {
            id: tab.project.id,
            name: tab.project.name,
            path: tab.project.path,
            createdAt: tab.project.createdAt,
            updatedAt: tab.project.updatedAt
          } : null,
          name: tab.name,
          selectedFiles: tab.selectedFiles ? Array.from(tab.selectedFiles) : []
        })),
        activeTabId: activeId
      };
      localStorage.setItem('smartrag-tabs', JSON.stringify(tabsData));
    } catch (error) {
      console.error('Failed to save tabs to localStorage:', error);
    }
  }, []);

  const loadTabsFromStorage = useCallback((): { tabs: ProjectTab[], activeTabId: string | null } => {
    try {
      const stored = localStorage.getItem('smartrag-tabs');
      if (stored) {
        const tabsData = JSON.parse(stored);

        // Защита от загрузки слишком большого количества табов
        if (tabsData.tabs && tabsData.tabs.length > 10) {
          console.warn(`Too many tabs in storage (${tabsData.tabs.length}), clearing...`);
          localStorage.removeItem('smartrag-tabs');
          return { tabs: [], activeTabId: null };
        }

        const restoredTabs: ProjectTab[] = tabsData.tabs.map((tabData: any) => ({
          id: tabData.id,
          project: tabData.project,
          name: tabData.name || (tabData.project ? tabData.project.name : 'Untitled'),
          isActive: false,
          selectedFiles: new Set(tabData.selectedFiles || [])
        }));

        return {
          tabs: restoredTabs,
          activeTabId: tabsData.activeTabId
        };
      }
    } catch (error) {
      console.error('Failed to load tabs from localStorage:', error);
      // Очищаем поврежденные данные
      localStorage.removeItem('smartrag-tabs');
    }

    return { tabs: [], activeTabId: null };
  }, []);

  // Tab management functions
  const switchToTab = useCallback((tabId: string) => {
    setTabs(prevTabs => {
      const updatedTabs = prevTabs.map(tab => ({
        ...tab,
        isActive: tab.id === tabId
      }));
      saveTabsToStorage(updatedTabs, tabId);
      return updatedTabs;
    });
    setActiveTabId(tabId);
  }, [saveTabsToStorage]);

  const createNewTab = useCallback((project: Project | null = null) => {
    // Если передан event объект или проект не передан, создаем пустой таб
    if (!project || typeof project !== 'object' || !project.id) {
      // Создаем новый пустой таб
      const newTab: ProjectTab = {
        id: `tab-${Date.now()}`,
        name: 'Untitled',
        project: null,
        isActive: true,
        selectedFiles: new Set<string>()
      };

      const newTabs = tabs.map(tab => ({ ...tab, isActive: false })).concat(newTab);
      setTabs(newTabs);
      setActiveTabId(newTab.id);
      saveTabsToStorage(newTabs, newTab.id);
      return;
    }

    // Проверяем, есть ли уже таб с этим проектом
    const existingTab = tabs.find(tab =>
      tab.project && tab.project.path === project.path
    );

    if (existingTab) {
      // Если таб уже существует, просто переключаемся на него
      switchToTab(existingTab.id);
      return;
    }

    const newTabId = `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newTab: ProjectTab = {
      id: newTabId,
      project,
      name: project.name,
      isActive: true,
      selectedFiles: new Set<string>()
    };

    setTabs(prevTabs => {
      const updatedTabs = prevTabs.map(tab => ({ ...tab, isActive: false }));
      const newTabs = [...updatedTabs, newTab];
      saveTabsToStorage(newTabs, newTabId);
      return newTabs;
    });
    setActiveTabId(newTabId);
  }, [tabs, switchToTab, saveTabsToStorage]);

  const updateTabProject = useCallback((tabId: string, project: Project) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId
          ? { ...tab, project, name: project.name }
          : tab
      )
    );
  }, []);

  // Функция для выбора проекта для текущего активного таба
  const handleProjectSelectForActiveTab = useCallback((project: Project) => {
    if (activeTabId) {
      const activeTab = tabs.find(tab => tab.id === activeTabId);
      if (activeTab && !activeTab.project) {
        // Присваиваем проект текущему пустому табу
        updateTabProject(activeTabId, project);
        return;
      }
    }

    // Если активный таб уже имеет проект, создаем новый таб принудительно
    const newTabId = `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newTab: ProjectTab = {
      id: newTabId,
      project,
      name: project.name,
      isActive: true,
      selectedFiles: new Set<string>()
    };

    setTabs(prevTabs => {
      const updatedTabs = prevTabs.map(tab => ({ ...tab, isActive: false }));
      const newTabs = [...updatedTabs, newTab];
      saveTabsToStorage(newTabs, newTabId);
      return newTabs;
    });
    setActiveTabId(newTabId);
  }, [activeTabId, tabs, updateTabProject, saveTabsToStorage]);

  const closeTab = useCallback((tabId: string) => {
    setTabs(prevTabs => {
      const filteredTabs = prevTabs.filter(tab => tab.id !== tabId);

      // If we're closing the active tab, activate another tab
      let newActiveTabId = activeTabId;
      if (tabId === activeTabId) {
        if (filteredTabs.length > 0) {
          const newActiveTab = filteredTabs[filteredTabs.length - 1];
          newActiveTabId = newActiveTab.id;
          setActiveTabId(newActiveTab.id);
        } else {
          newActiveTabId = null;
          setActiveTabId(null);
        }
      }

      saveTabsToStorage(filteredTabs, newActiveTabId);
      return filteredTabs;
    });
  }, [activeTabId, saveTabsToStorage]);

  const handleFileSelection = useCallback((selection: Set<string>) => {
    if (activeTabId) {
      setTabs(prevTabs =>
        prevTabs.map(tab =>
          tab.id === activeTabId
            ? { ...tab, selectedFiles: selection }
            : tab
        )
      );
    }
    console.log('Selected files:', Array.from(selection));
  }, [activeTabId]);

  const handleProjectSelect = useCallback((project: Project) => {
    if (activeTabId) {
      updateTabProject(activeTabId, project);
    } else {
      createNewTab(project);
    }
  }, [activeTabId, updateTabProject, createNewTab]);



  // Load tabs from storage on app initialization
  useEffect(() => {
    try {
      const { tabs: storedTabs, activeTabId: storedActiveTabId } = loadTabsFromStorage();

      // Дополнительная проверка валидности табов
      const validTabs = storedTabs.filter(tab =>
        tab && tab.id && typeof tab.id === 'string' &&
        (tab.name || tab.project?.name)
      );

      if (validTabs.length > 0 && validTabs.length === storedTabs.length) {
        setTabs(validTabs);
        setActiveTabId(storedActiveTabId);
      } else {
        // Если нет валидных табов или есть невалидные, создаем новый пустой таб
        if (storedTabs.length > 0) {
          console.warn('Found invalid tabs in storage, clearing...');
          localStorage.removeItem('smartrag-tabs');
        }
        createDefaultTab();
      }
    } catch (error) {
      console.error('Error loading tabs from storage:', error);
      localStorage.removeItem('smartrag-tabs');
      createDefaultTab();
    }

    function createDefaultTab() {
      const defaultTab: ProjectTab = {
        id: `tab-${Date.now()}`,
        name: 'Untitled',
        project: null,
        isActive: true,
        selectedFiles: new Set<string>()
      };
      setTabs([defaultTab]);
      setActiveTabId(defaultTab.id);
    }
  }, [loadTabsFromStorage]);

  // Ensure there's always at least one tab
  useEffect(() => {
    if (tabs.length === 0) {
      const defaultTab: ProjectTab = {
        id: `tab-${Date.now()}`,
        name: 'Untitled',
        project: null,
        isActive: true,
        selectedFiles: new Set<string>()
      };
      setTabs([defaultTab]);
      setActiveTabId(defaultTab.id);
    }
  }, [tabs.length]);

  // Save tabs whenever they change (debounced)
  useEffect(() => {
    if (tabs.length > 0) {
      const timeoutId = setTimeout(() => {
        saveTabsToStorage(tabs, activeTabId);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [tabs, activeTabId, saveTabsToStorage]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+T: New tab
      if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        createNewTab();
      }

      // Ctrl+W: Close current tab
      if (event.ctrlKey && event.key === 'w' && activeTabId && tabs.length > 1) {
        event.preventDefault();
        closeTab(activeTabId);
      }

      // Ctrl+Tab: Switch to next tab
      if (event.ctrlKey && event.key === 'Tab') {
        event.preventDefault();
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const nextIndex = (currentIndex + 1) % tabs.length;
        if (tabs[nextIndex]) {
          switchToTab(tabs[nextIndex].id);
        }
      }

      // Ctrl+Shift+Tab: Switch to previous tab
      if (event.ctrlKey && event.shiftKey && event.key === 'Tab') {
        event.preventDefault();
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        if (tabs[prevIndex]) {
          switchToTab(tabs[prevIndex].id);
        }
      }

      // Ctrl+1-9: Switch to specific tab
      if (event.ctrlKey && event.key >= '1' && event.key <= '9') {
        event.preventDefault();
        const tabIndex = parseInt(event.key) - 1;
        if (tabs[tabIndex]) {
          switchToTab(tabs[tabIndex].id);
        }
      }

      // Ctrl+B: Toggle sidebar
      if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        setSidebarCollapsed(!sidebarCollapsed);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [tabs, activeTabId, createNewTab, closeTab, switchToTab, sidebarCollapsed]);



  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', height: '100vh' }}>
        {/* Left Sidebar */}
        <Sidebar
          sidebarSection={sidebarSection}
          sidebarCollapsed={sidebarCollapsed}
          onSectionChange={setSidebarSection}
          onSidebarToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        {/* Main Content Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* Project Tab Bar - только если есть табы */}
          {tabs.length > 0 && (
            <ProjectTabBar
              tabs={tabs}
              activeTabId={activeTabId}
              onTabSwitch={switchToTab}
              onTabClose={closeTab}
              onNewTab={createNewTab}
              onProjectSelect={(tabId) => {
                console.log('Project select from tab:', tabId);
              }}
            />
          )}

          {/* Right Panel Content */}
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            {renderRightPanel()}
          </Box>
        </Box>


      </Box>
    </ThemeProvider>
  );
}

export default App;
