// FILE: package.json

{
  "name": "smart-rag",
  "productName": "SmartRAG",
  "version": "1.0.0",
  "description": "RAG Code Assistant with MCP support",
  "main": "dist-electron/main.js",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build && electron-builder",
    "gg": "ts-node _gg.ts"
  },
  "author": "AETHERIUM SWARM",
  "license": "MIT",
  "dependencies": {
    "@emotion/react": "^11.14.0",
    "@emotion/styled": "^11.14.1",
    "@lancedb/lancedb": "^0.21.2",
    "@mui/icons-material": "^7.2.0",
    "@mui/material": "^7.2.0",
    "@tailwindcss/postcss": "^4.1.11",
    "@tanstack/react-virtual": "^3.13.12",
    "better-sqlite3": "^12.2.0",
    "electron-squirrel-startup": "^1.0.1",
    "express": "^5.1.0",
    "langchain": "^0.3.30",
    "react": "^19.1.0",
    "react-arborist": "^3.4.3",
    "react-dom": "^19.1.0"
  },
  "devDependencies": {
    "@electron/rebuild": "^4.0.1",
    "@types/better-sqlite3": "^7.6.13",
    "@types/express": "^5.0.3",
    "@types/node": "^24.1.0",
    "@types/react": "^19.1.8",
    "@types/react-dom": "^19.1.6",
    "@vitejs/plugin-react": "^4.7.0",
    "chalk": "^5.4.1",
    "concurrently": "^9.2.0",
    "electron": "^37.2.4",
    "electron-builder": "^26.0.12",
    "glob": "^11.0.3",
    "npm-check-updates": "^18.0.2",
    "tailwindcss": "^4.0.0",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3",
    "vite": "^7.0.6",
    "vite-plugin-electron": "^0.29.0",
    "vite-plugin-electron-renderer": "^0.14.6",
    "wait-on": "^8.0.4"
  }
}


---

// FILE: src\main\main.ts

/**
 * @file src/main/main.ts
 * @description Main process entry point for the Electron application.
 * Handles window creation, application lifecycle events, and IPC.
 */

import { app, BrowserWindow, ipcMain, dialog, Menu, session } from 'electron';
import path from 'path';
import { basename } from 'path';
import fs from 'fs/promises';
import { Worker } from 'worker_threads';
import { mcpApiService } from './services/mcp_api';
import { isMac } from './utils/environment';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow: BrowserWindow | null = null;
let indexerWorker: Worker | null = null;

// Проверяем, запущен ли dev сервер
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;
const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'] || (isDev ? 'http://localhost:5173' : null);

console.log('[Main] Environment check:');
console.log('[Main] NODE_ENV:', process.env.NODE_ENV);
console.log('[Main] isPackaged:', app.isPackaged);
console.log('[Main] isDev:', isDev);
console.log('[Main] VITE_DEV_SERVER_URL:', VITE_DEV_SERVER_URL);

const createWindow = async () => {
  // Set up Content Security Policy for security
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          isDev
            ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: http://localhost:*; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:*; style-src 'self' 'unsafe-inline' http://localhost:*;"
            : "default-src 'self' data: blob:; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:;"
        ]
      }
    });
  });

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true, // Explicitly enable web security
    },
  });

  if (VITE_DEV_SERVER_URL) {
    console.log('[Main] Attempting to load Vite dev server:', VITE_DEV_SERVER_URL);
    await loadDevServer();
    mainWindow.webContents.openDevTools();
  } else {
    console.log('[Main] Loading production build...');
    mainWindow.loadFile(path.join(__dirname, '../index.html'));
    mainWindow.webContents.openDevTools();
  }
};

const loadDevServer = async (retries = 2, delay = 1500) => {
  console.log(`[Main] Starting dev server connection attempts to: ${VITE_DEV_SERVER_URL}`);
  
  for (let i = 0; i < retries; i++) {
    try {
      console.log(`[Main] Attempting to load Vite dev server (attempt ${i + 1}/${retries})...`);
      
      // Загружаем URL напрямую без fetch проверки
      await mainWindow!.loadURL(VITE_DEV_SERVER_URL!);
      console.log('[Main] ✅ Successfully loaded Vite dev server');
      return;
    } catch (error) {
      console.log(`[Main] ❌ Failed to load Vite dev server (attempt ${i + 1}/${retries}):`, error);
      if (i < retries - 1) {
        console.log(`[Main] ⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('[Main] ❌ Failed to load Vite dev server after all retries.');
  console.log('[Main] 🔄 Attempting to load fallback (production build)...');
  
  // Fallback to production build if available
  try {
    const fallbackPath = path.join(__dirname, '../index.html');
    console.log('[Main] Fallback path:', fallbackPath);
    await mainWindow!.loadFile(fallbackPath);
    console.log('[Main] ✅ Fallback loaded successfully');
  } catch (fallbackError) {
    console.error('[Main] ❌ Failed to load fallback file:', fallbackError);
    // Последний fallback - простой HTML
    mainWindow!.loadURL('data:text/html;charset=utf-8,<html><body style="background:#1e1e1e;color:white;padding:20px;font-family:Arial;"><h1>❌ Ошибка загрузки</h1><p>Не удалось загрузить ни dev сервер, ни production файлы.</p><p>Проверьте консоль для деталей.</p></body></html>');
  }
};

const createIndexerWorker = () => {
  const workerPath = path.join(__dirname, 'indexer.worker.js'); // Correct path
  indexerWorker = new Worker(workerPath);
  indexerWorker.on('message', (result) => {
    console.log('[Main] Received from worker:', result);
    mainWindow?.webContents.send('indexing-update', result);
  });
  indexerWorker.on('error', (error) => console.error('[Main] Worker error:', error));
  indexerWorker.on('exit', (code) => {
    if (code !== 0) console.error(`[Main] Worker stopped with exit code ${code}`);
  });
};

function createApplicationMenu() {
  const template = [
    ...(isMac ? [{
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    {
      label: 'File',
      submenu: [
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template as any);
  Menu.setApplicationMenu(menu);
}

app.on('ready', async () => {
  // Set a Content Security Policy
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          isDev
            ? "default-src 'self' 'unsafe-inline' data:; script-src 'self' 'unsafe-eval' 'unsafe-inline' data:"
            : "default-src 'self'"
        ]
      }
    });
  });

  createApplicationMenu();
  await createWindow();
  createIndexerWorker();
  mcpApiService.start();
});

app.on('window-all-closed', () => {
  mcpApiService.stop();
  indexerWorker?.terminate();
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

// --- IPC Handlers ---

ipcMain.on('start-indexing', (event, { filePath, projectId }) => {
  indexerWorker?.postMessage({ filePath, projectId });
});

ipcMain.handle('dialog:openDirectory', async () => {
  console.log('[Main] Opening directory dialog...');
  try {
    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow!, {
      properties: ['openDirectory']
    });
    console.log('[Main] Dialog result:', { canceled, filePaths });
    if (canceled) return null;
    return filePaths[0];
  } catch (error) {
    console.error('[Main] Error opening directory dialog:', error);
    return null;
  }
});

// --- Project IPC Handlers ---
import { sqliteService } from './services/sqlite';

ipcMain.handle('projects:getAll', async () => {
  return sqliteService.getAllProjects();
});

ipcMain.handle('projects:add', async (event, projectPath: string) => {
  console.log('[Main] Adding project:', projectPath);
  try {
    const name = basename(projectPath);
    console.log('[Main] Project name:', name);

    // Check for duplicates
    if (sqliteService.getProjectByName(name)) {
      throw new Error(`Project with name "${name}" already exists.`);
    }

    sqliteService.addProject(name, projectPath);
    const newProject = sqliteService.getProjectByName(name);
    console.log('[Main] Project added successfully:', newProject);
    return newProject;
  } catch (error: any) {
    console.error('[Main] Failed to add project:', error);
    dialog.showErrorBox('Error Adding Project', error.message);
    return null;
  }
});

ipcMain.handle('projects:delete', async (event, id: number) => {
  sqliteService.deleteProject(id);
});

// Recursive function to read directory structure
async function readDirectory(dirPath: string): Promise<any[]> {
  const dirents = await fs.readdir(dirPath, { withFileTypes: true });
  const files = await Promise.all(dirents.map(async (dirent) => {
    const res = path.resolve(dirPath, dirent.name);
    if (dirent.isDirectory()) {
      return {
        id: res,
        name: dirent.name,
        children: await readDirectory(res)
      };
    } else {
      return {
        id: res,
        name: dirent.name
      };
    }
  }));
  return files;
}

ipcMain.handle('fs:readDirectory', async (event, dirPath: string) => {
  try {
    return await readDirectory(dirPath);
  } catch (error) {
    console.error(`Failed to read directory ${dirPath}:`, error);
    return null;
  }
});

// New lazy loading function
async function readDirectoryLazy(dirPath: string): Promise<any[]> {
  const dirents = await fs.readdir(dirPath, { withFileTypes: true });
  const files = await Promise.all(dirents.map(async (dirent) => {
    const res = path.resolve(dirPath, dirent.name);
    const isDirectory = dirent.isDirectory();
    let hasChildren = false;
    if (isDirectory) {
      try {
        const subDirents = await fs.readdir(res, { withFileTypes: true });
        hasChildren = subDirents.length > 0;
      } catch (e) {
        // Ignore errors for sub-directories (e.g. permissions)
      }
    }
    return {
      id: res,
      name: dirent.name,
      children: isDirectory ? [] : undefined, // Important for lazy loading
      hasChildren: isDirectory && hasChildren,
    };
  }));
  
  // Sort directories first, then files, all alphabetically
  files.sort((a, b) => {
    if (a.hasChildren && !b.hasChildren) return -1;
    if (!a.hasChildren && b.hasChildren) return 1;
    return a.name.localeCompare(b.name);
  });

  return files;
}

ipcMain.handle('fs:readDirectoryLazy', async (event, dirPath: string) => {
  try {
    return await readDirectoryLazy(dirPath);
  } catch (error) {
    console.error(`Failed to read directory lazily ${dirPath}:`, error);
    return null;
  }
});

// App Data Management
ipcMain.handle('app:clearData', async () => {
  try {
    const { app } = require('electron');
    const fs = require('fs').promises;
    const path = require('path');

    // Получаем пути к данным приложения
    const userDataPath = app.getPath('userData');
    const appDataPath = app.getPath('appData');

    console.log('[Main] Clearing app data...');
    console.log('[Main] UserData path:', userDataPath);

    // Очищаем базу данных SQLite
    sqliteService.clearAllData();

    // Очищаем кеш и временные файлы
    const cachePath = path.join(userDataPath, 'cache');
    const tempPath = path.join(userDataPath, 'temp');

    try {
      await fs.rmdir(cachePath, { recursive: true });
      console.log('[Main] Cleared cache directory');
    } catch (error) {
      console.log('[Main] Cache directory not found or already cleared');
    }

    try {
      await fs.rmdir(tempPath, { recursive: true });
      console.log('[Main] Cleared temp directory');
    } catch (error) {
      console.log('[Main] Temp directory not found or already cleared');
    }

    console.log('[Main] App data cleared successfully');
  } catch (error) {
    console.error('[Main] Error clearing app data:', error);
    throw error;
  }
});


---

// FILE: src\main\services\indexer.worker.ts

/**
 * @file src/main/services/indexer.worker.ts
 * @description Worker thread for background document indexing.
 * Prevents blocking the main UI thread.
 */

import { parentPort } from 'worker_threads';
import fs from 'fs/promises';
import crypto from 'crypto';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
// import { llmService } from './llm'; // This would be a separate instance in a worker
// import { lanceDBService } from './lancedb';
// import { sqliteService } from './sqlite';

interface IndexingPayload {
  filePath: string;
  projectId: number;
}

const textSplitter = new RecursiveCharacterTextSplitter({
  chunkSize: 1024,
  chunkOverlap: 200,
});

async function processFile(payload: IndexingPayload) {
  const { filePath, projectId } = payload;
  parentPort?.postMessage({ status: 'progress', file: filePath, progress: 10 });

  console.log(`[Worker] Indexing: ${filePath}`);

  // 1. Read file content
  const content = await fs.readFile(filePath, 'utf-8');
  parentPort?.postMessage({ status: 'progress', file: filePath, progress: 30 });

  // 2. Create hash
  const hash = crypto.createHash('sha256').update(content).digest('hex');
  
  // TODO: Check against SQLite to see if file needs re-indexing
  // const existingDoc = sqliteService.getDocument(projectId, filePath);
  // if (existingDoc && existingDoc.fileHash === hash) {
  //   parentPort?.postMessage({ status: 'completed', file: filePath, progress: 100 });
  //   return;
  // }
  parentPort?.postMessage({ status: 'progress', file: filePath, progress: 40 });

  // 3. Chunk content
  const chunks = await textSplitter.splitText(content);
  parentPort?.postMessage({ status: 'progress', file: filePath, progress: 60 });

  // 4. Generate embeddings (dummy)
  // In a real scenario, you'd initialize llmService here or pass config
  const embeddings = await Promise.all(
    chunks.map(() => Array(768).fill(Math.random()))
  );
  parentPort?.postMessage({ status: 'progress', file: filePath, progress: 80 });

  // 5. Save to LanceDB (dummy)
  console.log(`[Worker] Would save ${chunks.length} chunks to LanceDB.`);
  
  // 6. Update SQLite (dummy)
  console.log(`[Worker] Would update SQLite for ${filePath} with hash ${hash}`);
  
  await new Promise(resolve => setTimeout(resolve, 200)); // Simulate DB work
  parentPort?.postMessage({ status: 'completed', file: filePath, progress: 100 });
}

parentPort?.on('message', (payload: IndexingPayload) => {
  processFile(payload).catch(error => {
    console.error(`[Worker] Error processing ${payload.filePath}:`, error);
    parentPort?.postMessage({ status: 'error', file: payload.filePath, error: error.message });
  });
});

console.log('[Worker] Indexer worker started and ready.');


---

// FILE: src\main\services\lancedb.ts

/**
 * @file src/main/services/lancedb.ts
 * @description Service for interacting with LanceDB.
 * Manages vector embeddings and similarity search.
 */

import { connect, Connection } from '@lancedb/lancedb';
import path from 'path';
import fs from 'fs';

const LANCE_DB_PATH = path.join(process.cwd(), 'lancedb');
if (!fs.existsSync(LANCE_DB_PATH)) {
  fs.mkdirSync(LANCE_DB_PATH, { recursive: true });
}

class LanceDBService {
  private db: Connection | null = null;

  async initialize(): Promise<void> {
    this.db = await connect(LANCE_DB_PATH);
  }

  async getTable(tableName: string) {
    if (!this.db) {
      await this.initialize();
    }
    try {
      const table = await this.db!.openTable(tableName);
      return table;
    } catch (error) {
      // Assuming table not found, we can create it.
      // The actual schema will depend on the embedding model.
      // For now, we'll return null and handle creation separately.
      return null;
    }
  }

  async createTable(tableName: string, embeddingSize: number) {
    if (!this.db) {
      await this.initialize();
    }
    // Dummy data to infer schema. LanceDB requires data to create a table.
    const dummyData = [{
      vector: Array(embeddingSize).fill(0),
      text: 'dummy',
      metadata: { file: 'dummy.txt', startLine: 0, endLine: 0, project: 'dummy' }
    }];
    
    const table = await this.db!.createTable(tableName, dummyData);
    return table;
  }
}

export const lanceDBService = new LanceDBService();


---

// FILE: src\main\services\llm.ts

/**
 * @file src/main/services/llm.ts
 * @description Client for interacting with LLMs (Gemini or Ollama).
 * Handles text generation and embedding creation.
 */

import { app } from 'electron';

type LLMProvider = 'gemini' | 'ollama';

interface LLMConfig {
  provider: LLMProvider;
  apiKey?: string; // For Gemini
  model: string;
}

class LLMService {
  private config: LLMConfig = {
    provider: 'ollama', // Default to local-first
    model: 'llama3:8b',
  };

  setConfig(newConfig: Partial<LLMConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  async generateEmbedding(text: string): Promise<number[]> {
    // TODO: Implement actual API calls to Gemini or Ollama
    console.log(`Generating embedding for: "${text.substring(0, 50)}..."`);
    // This is a dummy implementation.
    // The size should match the model's output, e.g., 768 for nomic-embed-text
    const DUMMY_EMBEDDING_SIZE = 768;
    return Array(DUMMY_EMBEDDING_SIZE).fill(Math.random());
  }

  async generateResponse(prompt: string): Promise<string> {
    // TODO: Implement actual API calls to Gemini or Ollama
    console.log(`Generating response for prompt: "${prompt.substring(0, 100)}..."`);
    // This is a dummy implementation.
    return `This is a dummy response for the prompt: "${prompt.substring(0, 100)}..."`;
  }
}

export const llmService = new LLMService();


---

// FILE: src\main\services\mcp_api.ts

/**
 * @file src/main/services/mcp_api.ts
 * @description Local MCP-compliant API server using Express.
 * Provides context to AI coding tools.
 */

import express from 'express';
import { llmService } from './llm';
// import { lanceDBService } from './lancedb'; // Will be used later

const PORT = 4888; // Port for the local MCP server

class MCPApiService {
  private app: express.Application;
  private server: any;

  constructor() {
    this.app = express();
    this.app.use(express.json());
    this.setupRoutes();
  }

  private setupRoutes() {
    this.app.get('/mcp/v1/status', (req, res) => {
      res.json({ status: 'running', timestamp: new Date().toISOString() });
    });

    this.app.post('/mcp/v1/context', async (req, res) => {
      const { question } = req.body;
      if (!question) {
        return res.status(400).json({ error: 'Missing "question" in request body' });
      }

      // This is a simplified flow for now.
      // 1. Decompose question (TODO)
      // 2. Get embeddings for sub-questions (using dummy implementation)
      const embedding = await llmService.generateEmbedding(question);
      
      // 3. Search in LanceDB (TODO)
      const searchResults = `Dummy search results for embedding: [${embedding.slice(0, 4).join(', ')}...]`;

      // 4. Generate response with LLM (using dummy implementation)
      const prompt = `Context: ${searchResults}\n\nQuestion: ${question}`;
      const response = await llmService.generateResponse(prompt);

      res.json({
        context: response,
        sources: [{ uri: 'dummy/file.ts', text: 'dummy content' }],
      });
    });
  }

  start() {
    if (this.server) {
      console.log('MCP API server is already running.');
      return;
    }
    this.server = this.app.listen(PORT, '127.0.0.1', () => {
      console.log(`MCP API server started at http://127.0.0.1:${PORT}`);
    });
  }

  stop() {
    if (this.server) {
      this.server.close(() => {
        console.log('MCP API server stopped.');
        this.server = null;
      });
    }
  }
}

export const mcpApiService = new MCPApiService();


---

// FILE: src\main\services\sqlite.ts

/**
 * @file src/main/services/sqlite.ts
 * @description Service for interacting with the SQLite database.
 * Manages projects, metadata, and history.
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

const DB_PATH = path.join(process.cwd(), 'database');
if (!fs.existsSync(DB_PATH)) {
  fs.mkdirSync(DB_PATH, { recursive: true });
}

const db = new Database(path.join(DB_PATH, 'smart-rag.db'));

// --- Schema Initialization ---
const initSchema = () => {
  db.exec(`
    CREATE TABLE IF NOT EXISTS projects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      path TEXT NOT NULL UNIQUE,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  db.exec(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      projectId INTEGER NOT NULL,
      filePath TEXT NOT NULL,
      fileHash TEXT NOT NULL,
      lastIndexedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects (id) ON DELETE CASCADE,
      UNIQUE (projectId, filePath)
    );
  `);
};

initSchema();

// --- Service Interface ---

export interface Project {
  id: number;
  name: string;
  path: string;
  createdAt: string;
}

class SQLiteService {
  addProject(name: string, projectPath: string): Database.RunResult {
    const stmt = db.prepare('INSERT INTO projects (name, path) VALUES (?, ?)');
    return stmt.run(name, projectPath);
  }

  getProjectByName(name: string): Project | null {
    const stmt = db.prepare('SELECT * FROM projects WHERE name = ?');
    return (stmt.get(name) as Project) || null;
  }

  getAllProjects(): Project[] {
    const stmt = db.prepare('SELECT * FROM projects ORDER BY name');
    return stmt.all() as Project[];
  }

  deleteProject(id: number): Database.RunResult {
    const stmt = db.prepare('DELETE FROM projects WHERE id = ?');
    return stmt.run(id);
  }

  clearAllData(): void {
    try {
      // Очищаем все таблицы
      db.exec('DELETE FROM documents');
      db.exec('DELETE FROM projects');

      // Сбрасываем автоинкремент
      db.exec('DELETE FROM sqlite_sequence WHERE name IN ("projects", "documents")');

      console.log('[SQLite] All data cleared successfully');
    } catch (error) {
      console.error('[SQLite] Error clearing data:', error);
      throw error;
    }
  }
}

export const sqliteService = new SQLiteService();


---

// FILE: src\main\utils\environment.ts

export const isDev = process.env.NODE_ENV === 'development';
export const isProd = process.env.NODE_ENV === 'production';
export const isTest = process.env.NODE_ENV === 'test';

export const getAppDataPath = (): string => {
  const { app } = require('electron');
  return app.getPath('userData');
};

export const getResourcesPath = (): string => {
  const { app } = require('electron');
  return process.resourcesPath || app.getAppPath();
};

export const isMac = process.platform === 'darwin';
export const isWindows = process.platform === 'win32';
export const isLinux = process.platform === 'linux';


---

// FILE: src\renderer\App.tsx

/**
 * @file src/renderer/App.tsx
 * @description Root React component for the application with tabbed project interface.
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Box, CssBaseline, ThemeProvider, createTheme, Typography, alpha, Button } from '@mui/material';
import { Add, FolderOpen } from '@mui/icons-material';

import { ProjectTabBar } from './components/ProjectTabBar';
import { Sidebar } from './components/Sidebar';
import { ProjectsPanel } from './components/ProjectsPanel';
import { FileExplorerPanel } from './components/FileExplorerPanel';
import { DashboardPanel } from './components/DashboardPanel';
import { ChatPanel } from './components/ChatPanel';
import { SettingsPanel } from './components/SettingsPanel';
import { EmptyTabContent } from './components/EmptyTabContent';
import type { Project } from '../shared/ipc.d.ts';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#9333ea',
      light: '#a855f7',
      dark: '#7c3aed',
    },
    secondary: {
      main: '#c084fc',
      light: '#d8b4fe',
      dark: '#a855f7',
    },
    background: {
      default: '#0f0f0f',
      paper: '#1a1a1a',
    },
    text: {
      primary: '#f1f5f9',
      secondary: '#94a3b8',
      disabled: '#64748b',
    },
    divider: '#2a2a2a',
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h4: {
      fontWeight: 500,
      fontSize: '1.25rem',
    },
    body1: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.75rem',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
          fontFeatureSettings: '"cv02","cv03","cv04","cv11"',
        },
        '*::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '*::-webkit-scrollbar-track': {
          background: '#1a1a1a',
        },
        '*::-webkit-scrollbar-thumb': {
          background: '#9333ea',
          borderRadius: '4px',
          '&:hover': {
            background: '#a855f7',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          borderRadius: 8,
          padding: '8px 16px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(147, 51, 234, 0.3)',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #9333ea 0%, #a855f7 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #7c3aed 0%, #9333ea 100%)',
          },
        },
        outlined: {
          borderColor: '#9333ea',
          color: '#d8b4fe',
          '&:hover': {
            borderColor: '#a855f7',
            backgroundColor: alpha('#9333ea', 0.1),
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#1a1a1a',
          border: '1px solid #2a2a2a',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: 48,
          backgroundColor: '#1a1a1a',
          borderBottom: '1px solid #2a2a2a',
        },
        indicator: {
          background: 'linear-gradient(90deg, #9333ea 0%, #a855f7 100%)',
          height: 3,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          fontSize: '0.875rem',
          color: '#94a3b8',
          minHeight: 48,
          padding: '12px 16px',
          '&.Mui-selected': {
            color: '#d8b4fe',
          },
          '&:hover': {
            color: '#c084fc',
            backgroundColor: alpha('#9333ea', 0.1),
          },
        },
      },
    },
  },
});



// Define the project tab interface
interface ProjectTab {
  id: string;
  project: Project | null;
  name: string;
  isActive: boolean;
  selectedFiles: Set<string>;
}

// Define sidebar section types
type SidebarSection = 'files' | 'projects' | 'dashboard' | 'chat' | 'settings';

function App() {
  // Tab management state
  const [tabs, setTabs] = useState<ProjectTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [sidebarSection, setSidebarSection] = useState<SidebarSection>('projects');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Get the currently active tab
  const activeTab = tabs.find(tab => tab.id === activeTabId) || null;

  // Handle file selection change
  const handleFileSelectionChange = useCallback((selection: string[]) => {
    if (activeTabId) {
      setTabs(prevTabs =>
        prevTabs.map(tab =>
          tab.id === activeTabId
            ? { ...tab, selectedFiles: new Set(selection) }
            : tab
        )
      );
    }
  }, [activeTabId]);

  // Render the right panel based on selected sidebar section
  const renderRightPanel = () => {
    // Если активный таб не имеет проекта, показываем интерфейс выбора проекта
    if (activeTab && !activeTab.project) {
      return (
        <EmptyTabContent
          onProjectSelect={handleProjectSelectForActiveTab}
          openProjects={tabs.map(tab => tab.project)}
        />
      );
    }

    switch (sidebarSection) {
      case 'files':
        return activeTab?.project ? (
          <FileExplorerPanel
            project={activeTab.project}
            onSelectionChange={handleFileSelectionChange}
          />
        ) : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <FolderOpen sx={{ fontSize: 64, mb: 2, color: '#374151' }} />
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;

      case 'projects':
        return (
          <ProjectsPanel
            onProjectSelect={handleProjectSelect}
            openProjects={tabs.map(tab => tab.project).filter(Boolean)}
            compact={true}
          />
        );

      case 'dashboard':
        return activeTab?.project ? <DashboardPanel project={activeTab.project} /> : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'chat':
        return activeTab?.project ? <ChatPanel project={activeTab.project} /> : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'settings':
        return <SettingsPanel />;
      default:
        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            p: 3
          }}>
            <Typography variant="h6">
              Welcome to SmartRAG
            </Typography>
          </Box>
        );
    }
  };

  // Tab persistence functions
  const saveTabsToStorage = useCallback((tabsToSave: ProjectTab[], activeId: string | null) => {
    try {
      const tabsData = {
        tabs: tabsToSave.map(tab => ({
          id: tab.id,
          project: tab.project ? {
            id: tab.project.id,
            name: tab.project.name,
            path: tab.project.path,
            createdAt: tab.project.createdAt,
            updatedAt: tab.project.updatedAt
          } : null,
          name: tab.name,
          selectedFiles: tab.selectedFiles ? Array.from(tab.selectedFiles) : []
        })),
        activeTabId: activeId
      };
      localStorage.setItem('smartrag-tabs', JSON.stringify(tabsData));
    } catch (error) {
      console.error('Failed to save tabs to localStorage:', error);
    }
  }, []);

  const loadTabsFromStorage = useCallback((): { tabs: ProjectTab[], activeTabId: string | null } => {
    try {
      const stored = localStorage.getItem('smartrag-tabs');
      if (stored) {
        const tabsData = JSON.parse(stored);

        // Защита от загрузки слишком большого количества табов
        if (tabsData.tabs && tabsData.tabs.length > 10) {
          console.warn(`Too many tabs in storage (${tabsData.tabs.length}), clearing...`);
          localStorage.removeItem('smartrag-tabs');
          return { tabs: [], activeTabId: null };
        }

        const restoredTabs: ProjectTab[] = tabsData.tabs.map((tabData: any) => ({
          id: tabData.id,
          project: tabData.project,
          name: tabData.name || (tabData.project ? tabData.project.name : 'Untitled'),
          isActive: false,
          selectedFiles: new Set(tabData.selectedFiles || [])
        }));

        return {
          tabs: restoredTabs,
          activeTabId: tabsData.activeTabId
        };
      }
    } catch (error) {
      console.error('Failed to load tabs from localStorage:', error);
      // Очищаем поврежденные данные
      localStorage.removeItem('smartrag-tabs');
    }

    return { tabs: [], activeTabId: null };
  }, []);

  // Tab management functions
  const switchToTab = useCallback((tabId: string) => {
    setTabs(prevTabs => {
      const updatedTabs = prevTabs.map(tab => ({
        ...tab,
        isActive: tab.id === tabId
      }));
      saveTabsToStorage(updatedTabs, tabId);
      return updatedTabs;
    });
    setActiveTabId(tabId);
  }, [saveTabsToStorage]);

  const createNewTab = useCallback((project: Project | null = null) => {
    // Если передан event объект или проект не передан, создаем пустой таб
    if (!project || typeof project !== 'object' || !project.id) {
      // Создаем новый пустой таб
      const newTab: ProjectTab = {
        id: `tab-${Date.now()}`,
        name: 'Untitled',
        project: null,
        isActive: true,
        selectedFiles: new Set<string>()
      };

      const newTabs = tabs.map(tab => ({ ...tab, isActive: false })).concat(newTab);
      setTabs(newTabs);
      setActiveTabId(newTab.id);
      saveTabsToStorage(newTabs, newTab.id);
      return;
    }

    // Проверяем, есть ли уже таб с этим проектом
    const existingTab = tabs.find(tab =>
      tab.project && tab.project.path === project.path
    );

    if (existingTab) {
      // Если таб уже существует, просто переключаемся на него
      switchToTab(existingTab.id);
      return;
    }

    const newTabId = `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newTab: ProjectTab = {
      id: newTabId,
      project,
      name: project.name,
      isActive: true,
      selectedFiles: new Set<string>()
    };

    setTabs(prevTabs => {
      const updatedTabs = prevTabs.map(tab => ({ ...tab, isActive: false }));
      const newTabs = [...updatedTabs, newTab];
      saveTabsToStorage(newTabs, newTabId);
      return newTabs;
    });
    setActiveTabId(newTabId);
  }, [tabs, switchToTab, saveTabsToStorage]);

  const updateTabProject = useCallback((tabId: string, project: Project) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId
          ? { ...tab, project, name: project.name }
          : tab
      )
    );
  }, []);

  // Функция для выбора проекта для текущего активного таба
  const handleProjectSelectForActiveTab = useCallback((project: Project) => {
    console.log('App.handleProjectSelectForActiveTab called with:', project);
    console.log('Current activeTabId:', activeTabId);
    console.log('Current tabs:', tabs.map(t => ({ id: t.id, name: t.name, hasProject: !!t.project })));

    if (activeTabId) {
      const activeTab = tabs.find(tab => tab.id === activeTabId);
      if (activeTab && !activeTab.project) {
        console.log('Assigning project to empty tab');
        // Присваиваем проект текущему пустому табу
        updateTabProject(activeTabId, project);
        return;
      }
    }

    // Если активный таб уже имеет проект, создаем новый таб принудительно
    const newTabId = `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newTab: ProjectTab = {
      id: newTabId,
      project,
      name: project.name,
      isActive: true,
      selectedFiles: new Set<string>()
    };

    setTabs(prevTabs => {
      const updatedTabs = prevTabs.map(tab => ({ ...tab, isActive: false }));
      const newTabs = [...updatedTabs, newTab];
      saveTabsToStorage(newTabs, newTabId);
      return newTabs;
    });
    setActiveTabId(newTabId);
  }, [activeTabId, tabs, updateTabProject, saveTabsToStorage]);

  const closeTab = useCallback((tabId: string) => {
    setTabs(prevTabs => {
      const filteredTabs = prevTabs.filter(tab => tab.id !== tabId);

      // If we're closing the active tab, activate another tab
      let newActiveTabId = activeTabId;
      if (tabId === activeTabId) {
        if (filteredTabs.length > 0) {
          const newActiveTab = filteredTabs[filteredTabs.length - 1];
          newActiveTabId = newActiveTab.id;
          setActiveTabId(newActiveTab.id);
        } else {
          newActiveTabId = null;
          setActiveTabId(null);
        }
      }

      saveTabsToStorage(filteredTabs, newActiveTabId);
      return filteredTabs;
    });
  }, [activeTabId, saveTabsToStorage]);

  const handleFileSelection = useCallback((selection: Set<string>) => {
    if (activeTabId) {
      setTabs(prevTabs =>
        prevTabs.map(tab =>
          tab.id === activeTabId
            ? { ...tab, selectedFiles: selection }
            : tab
        )
      );
    }
    console.log('Selected files:', Array.from(selection));
  }, [activeTabId]);

  const handleProjectSelect = useCallback((project: Project) => {
    if (activeTabId) {
      updateTabProject(activeTabId, project);
    } else {
      createNewTab(project);
    }
  }, [activeTabId, updateTabProject, createNewTab]);



  // Load tabs from storage on app initialization
  useEffect(() => {
    try {
      const { tabs: storedTabs, activeTabId: storedActiveTabId } = loadTabsFromStorage();

      // Дополнительная проверка валидности табов
      const validTabs = storedTabs.filter(tab =>
        tab && tab.id && typeof tab.id === 'string' &&
        (tab.name || tab.project?.name)
      );

      if (validTabs.length > 0 && validTabs.length === storedTabs.length) {
        setTabs(validTabs);
        setActiveTabId(storedActiveTabId);
      } else {
        // Если нет валидных табов или есть невалидные, создаем новый пустой таб
        if (storedTabs.length > 0) {
          console.warn('Found invalid tabs in storage, clearing...');
          localStorage.removeItem('smartrag-tabs');
        }
        createDefaultTab();
      }
    } catch (error) {
      console.error('Error loading tabs from storage:', error);
      localStorage.removeItem('smartrag-tabs');
      createDefaultTab();
    }

    function createDefaultTab() {
      const defaultTab: ProjectTab = {
        id: `tab-${Date.now()}`,
        name: 'Untitled',
        project: null,
        isActive: true,
        selectedFiles: new Set<string>()
      };
      setTabs([defaultTab]);
      setActiveTabId(defaultTab.id);
    }
  }, [loadTabsFromStorage]);

  // Ensure there's always at least one tab
  useEffect(() => {
    if (tabs.length === 0) {
      const defaultTab: ProjectTab = {
        id: `tab-${Date.now()}`,
        name: 'Untitled',
        project: null,
        isActive: true,
        selectedFiles: new Set<string>()
      };
      setTabs([defaultTab]);
      setActiveTabId(defaultTab.id);
    }
  }, [tabs.length]);

  // Save tabs whenever they change (debounced)
  useEffect(() => {
    if (tabs.length > 0) {
      const timeoutId = setTimeout(() => {
        saveTabsToStorage(tabs, activeTabId);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [tabs, activeTabId, saveTabsToStorage]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+T: New tab
      if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        createNewTab();
      }

      // Ctrl+W: Close current tab
      if (event.ctrlKey && event.key === 'w' && activeTabId && tabs.length > 1) {
        event.preventDefault();
        closeTab(activeTabId);
      }

      // Ctrl+Tab: Switch to next tab
      if (event.ctrlKey && event.key === 'Tab') {
        event.preventDefault();
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const nextIndex = (currentIndex + 1) % tabs.length;
        if (tabs[nextIndex]) {
          switchToTab(tabs[nextIndex].id);
        }
      }

      // Ctrl+Shift+Tab: Switch to previous tab
      if (event.ctrlKey && event.shiftKey && event.key === 'Tab') {
        event.preventDefault();
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        if (tabs[prevIndex]) {
          switchToTab(tabs[prevIndex].id);
        }
      }

      // Ctrl+1-9: Switch to specific tab
      if (event.ctrlKey && event.key >= '1' && event.key <= '9') {
        event.preventDefault();
        const tabIndex = parseInt(event.key) - 1;
        if (tabs[tabIndex]) {
          switchToTab(tabs[tabIndex].id);
        }
      }

      // Ctrl+B: Toggle sidebar
      if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        setSidebarCollapsed(!sidebarCollapsed);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [tabs, activeTabId, createNewTab, closeTab, switchToTab, sidebarCollapsed]);



  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', height: '100vh' }}>
        {/* Left Sidebar */}
        <Sidebar
          sidebarSection={sidebarSection}
          sidebarCollapsed={sidebarCollapsed}
          onSectionChange={setSidebarSection}
          onSidebarToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        {/* Main Content Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* Project Tab Bar - только если есть табы */}
          {tabs.length > 0 && (
            <ProjectTabBar
              tabs={tabs}
              activeTabId={activeTabId}
              onTabSwitch={switchToTab}
              onTabClose={closeTab}
              onNewTab={createNewTab}
              onProjectSelect={(tabId) => {
                console.log('Project select from tab:', tabId);
              }}
            />
          )}

          {/* Right Panel Content */}
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            {renderRightPanel()}
          </Box>
        </Box>


      </Box>
    </ThemeProvider>
  );
}

export default App;


---

// FILE: src\renderer\TestApp.tsx

/**
 * @file src/renderer/TestApp.tsx
 * @description Простой тестовый React компонент для диагностики
 */

import React from 'react';

const TestApp: React.FC = () => {
  console.log('[TestApp] Компонент рендерится!');

  return (
    <div style={{
      backgroundColor: '#1e1e1e',
      color: 'white',
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#4CAF50' }}>✅ React Тест Успешен!</h1>
      
      <div style={{
        backgroundColor: '#2d2d2d',
        border: '2px solid #4CAF50',
        padding: '15px',
        margin: '10px 0',
        borderRadius: '5px'
      }}>
        <h2>🔧 Диагностическая информация:</h2>
        <ul>
          <li>✅ React компонент загружен</li>
          <li>✅ JSX работает</li>
          <li>✅ Стили применяются</li>
          <li>✅ TypeScript компилируется</li>
        </ul>
      </div>

      <div style={{
        backgroundColor: '#2d2d2d',
        border: '2px solid #2196F3',
        padding: '15px',
        margin: '10px 0',
        borderRadius: '5px'
      }}>
        <h2>🔍 Проверка API:</h2>
        <p>ElectronAPI доступен: {window.electronAPI ? '✅ Да' : '❌ Нет'}</p>
        <p>Время загрузки: {new Date().toLocaleTimeString()}</p>
      </div>

      <div style={{
        backgroundColor: '#2d2d2d',
        border: '2px solid #FF9800',
        padding: '15px',
        margin: '10px 0',
        borderRadius: '5px'
      }}>
        <h2>📝 Следующие шаги:</h2>
        <p>Если вы видите этот экран, то React работает корректно!</p>
        <p>Можно переходить к тестированию основного приложения.</p>
      </div>
    </div>
  );
};

export default TestApp;


---

// FILE: src\renderer\components\ChatPanel.tsx

/**
 * @file src/renderer/components/ChatPanel.tsx
 * @description Chat panel component for AI assistant interface
 */

import React from 'react';
import { Box, Typography } from '@mui/material';

interface ChatPanelProps {
  project?: any;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({ project }) => {
  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center'
      }}>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          Chat interface will be implemented here
        </Typography>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\CompactProjectExplorer.tsx

/**
 * @file src/renderer/components/CompactProjectExplorer.tsx
 * @description Compact project manager for sidebar with minimal UI
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { Project } from '../../shared/ipc.d';
import '../styles/globals.css';

// Optimized SVG icons for maximum performance
const ICONS = {
  add: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>`,
  delete: `<svg class="size-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>`,
  folder: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
  folderOpen: `<svg class="size-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
  storage: `<svg class="size-8" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z"></path></svg>`,
} as const;

interface CompactProjectExplorerProps {
  onProjectSelect: (project: Project | null) => void;
  openProjects?: Project[];
  disableAutoSelection?: boolean;
}

export const CompactProjectExplorer = ({ onProjectSelect, openProjects = [], disableAutoSelection = false }: CompactProjectExplorerProps) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [hoveredProjectId, setHoveredProjectId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasManualSelection, setHasManualSelection] = useState(false);

  // Optimized project fetching with error handling
  const fetchProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      const projs = await window.electronAPI.getAllProjects();
      setProjects(projs);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Separate effect for initial project selection
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // Handle initial project selection separately
  useEffect(() => {
    console.log('Compact Project selection effect triggered:', {
      projectsLength: projects.length,
      selectedProjectId,
      hasManualSelection,
      disableAutoSelection,
      projects: projects.map(p => ({ id: p.id, name: p.name }))
    });

    if (projects.length > 0 && selectedProjectId === null && !hasManualSelection && !disableAutoSelection) {
      console.log('Auto-selecting first project');
      const firstProject = projects[0];
      setSelectedProjectId(firstProject.id);
      onProjectSelect(firstProject);
    } else if (projects.length === 0 && selectedProjectId !== null) {
      console.log('Clearing selection - no projects');
      setSelectedProjectId(null);
      onProjectSelect(null);
      setHasManualSelection(false);
    }
  }, [projects, selectedProjectId, onProjectSelect, hasManualSelection, disableAutoSelection]);

  // Optimized event handlers with loading states
  const handleAddProject = useCallback(async (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    try {
      setIsLoading(true);
      console.log('Opening directory dialog...');
      const directoryPath = await window.electronAPI.openDirectoryDialog();
      console.log('Directory selected:', directoryPath);

      if (directoryPath) {
        console.log('Adding project...');
        const newProject = await window.electronAPI.addProject(directoryPath);
        console.log('Project added:', newProject);
        await fetchProjects();

        // Автоматически выбираем новый добавленный проект
        if (newProject) {
          setSelectedProjectId(newProject.id);
          setHasManualSelection(true);
          onProjectSelect(newProject);
        }
      } else {
        console.log('No directory selected');
      }
    } catch (error) {
      console.error('Failed to add project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects, onProjectSelect]);

  const handleDeleteProject = useCallback(async (projectId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      setIsLoading(true);
      await window.electronAPI.deleteProject(projectId);
      await fetchProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects]);

  const handleSelectProject = useCallback((project: Project) => {
    console.log('handleSelectProject called with:', project);
    setSelectedProjectId(project.id);
    setHasManualSelection(true);
    onProjectSelect(project);
  }, [onProjectSelect]);

  // Memoized project list for better performance
  const projectList = useMemo(() => projects, [projects]);

  return (
    <div className="p-3 h-full flex flex-col gpu-layer">
      {/* Add button - compact */}
      <div className="flex justify-end items-center mb-4 pb-2 border-b border-primary-500/20">
        <button
          onClick={handleAddProject}
          disabled={isLoading}
          className="
            group relative flex items-center gap-1.5 px-3 py-1.5
            bg-gradient-to-r from-primary-600 to-primary-500
            hover:from-primary-700 hover:to-primary-600
            disabled:from-gray-600 disabled:to-gray-500
            text-white text-xs font-medium rounded-md
            transition-all duration-200 ease-out
            shadow-md shadow-primary-500/25 hover:shadow-primary-500/40
            focus-ring gpu-layer
          "
          title="Add new project"
        >
          <div
            className={`transition-transform duration-200 ${isLoading ? 'animate-spin' : ''}`}
            dangerouslySetInnerHTML={{ __html: ICONS.add }}
          />
          <span>Add</span>
        </button>
      </div>

      {/* Project list with compact scrolling */}
      <div className="flex-1 overflow-y-auto scrollbar-ultra-smooth">
        {projectList.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-6 animate-fade-in gpu-layer">
            <div
              className="mb-3 text-gray-600 opacity-60"
              dangerouslySetInnerHTML={{ __html: ICONS.storage }}
            />
            <h3 className="text-sm font-medium text-gray-400 mb-1">
              No projects yet
            </h3>
            <p className="text-xs text-gray-500">
              Click "Add" to get started
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {projectList.map((project, index) => (
              <div
                key={project.id}
                className="
                  group relative animate-slide-in gpu-layer
                  transition-all duration-200 ease-out
                "
                style={{ animationDelay: `${index * 30}ms` }}
                onMouseEnter={() => setHoveredProjectId(project.id)}
                onMouseLeave={() => setHoveredProjectId(null)}
              >
                <button
                  onClick={() => handleSelectProject(project)}
                  className={`
                    w-full text-left p-2 rounded-lg border transition-all duration-200 ease-out
                    backdrop-blur-sm focus-ring gpu-layer relative overflow-hidden
                    ${selectedProjectId === project.id
                      ? 'bg-gradient-to-br from-primary-500/20 to-primary-600/10 border-primary-500/40 shadow-md shadow-primary-500/20'
                      : 'bg-gray-800/40 border-gray-700/30 hover:bg-gray-800/60 hover:border-gray-600/50'
                    }
                  `}
                >
                  <div className="flex items-center gap-2 relative z-10">
                    {/* Project Icon */}
                    <div
                      className={`
                        transition-all duration-200 ease-out flex-shrink-0
                        ${selectedProjectId === project.id ? 'text-primary-300' : 'text-gray-400'}
                      `}
                      dangerouslySetInnerHTML={{
                        __html: selectedProjectId === project.id ? ICONS.folderOpen : ICONS.folder
                      }}
                    />

                    {/* Project Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className={`
                        text-xs font-medium mb-0.5 transition-colors duration-200 truncate
                        ${selectedProjectId === project.id ? 'text-primary-200' : 'text-gray-200'}
                      `}>
                        {project.name}
                      </h3>

                      <div className="flex items-center gap-1">
                        {(() => {
                          const isOpen = openProjects.some(openProject => openProject?.path === project.path);
                          return (
                            <span className={`
                              inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium
                              transition-colors duration-200
                              ${isOpen
                                ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                                : selectedProjectId === project.id
                                  ? 'bg-primary-500/20 text-primary-300 border border-primary-500/30'
                                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'
                              }
                            `}>
                              {isOpen ? 'Open' : 'Ready'}
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>

                  {/* Subtle background glow for selected project */}
                  {selectedProjectId === project.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-primary-600/5 rounded-lg opacity-50" />
                  )}
                </button>
                {/* Delete button with smooth animations - compact */}
                <button
                  onClick={(e) => handleDeleteProject(project.id, e)}
                  disabled={isLoading}
                  className={`
                    absolute top-1 right-1 p-1 rounded-md
                    transition-all duration-200 ease-out z-20
                    hover:bg-red-500/20 hover:scale-110 active:scale-95
                    focus-ring disabled:opacity-50 disabled:cursor-not-allowed
                    ${hoveredProjectId === project.id
                      ? 'opacity-100 text-red-400'
                      : 'opacity-0 text-transparent pointer-events-none'
                    }
                  `}
                  title="Delete project"
                >
                  <div dangerouslySetInnerHTML={{ __html: ICONS.delete }} />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};


---

// FILE: src\renderer\components\DashboardPanel.tsx

/**
 * @file src/renderer/components/DashboardPanel.tsx
 * @description Dashboard panel component for indexing dashboard
 */

import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, LinearProgress, List, ListItem, ListItemText, Chip } from '@mui/material';
import type { IndexingUpdatePayload } from '../../shared/ipc.d';

interface LogEntry {
  id: number;
  timestamp: string;
  message: string;
  type: 'info' | 'error' | 'success';
}

interface DashboardPanelProps {
  project?: any;
}

export const DashboardPanel: React.FC<DashboardPanelProps> = ({ project }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [currentProgress, setCurrentProgress] = useState<{ file: string; progress: number } | null>(null);
  const logContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const unsubscribe = window.electronAPI.onIndexingUpdate((payload: IndexingUpdatePayload) => {
      const timestamp = new Date().toLocaleTimeString();

      if (payload.status === 'progress') {
        setCurrentProgress({ file: payload.file, progress: payload.progress || 0 });
      } else {
        setCurrentProgress(null);

        const logEntry: LogEntry = {
          id: Date.now(),
          timestamp,
          message: payload.status === 'error'
            ? `Error indexing ${payload.file}: ${payload.error}`
            : `Successfully indexed ${payload.file}`,
          type: payload.status === 'error' ? 'error' : 'success'
        };

        setLogs(prev => [logEntry, ...prev].slice(0, 100)); // Keep last 100 logs
      }
    });

    return unsubscribe;
  }, []);

  // Auto-scroll to top when new logs are added
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = 0;
    }
  }, [logs]);

  return (
    <Box sx={{
      p: 3,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: 3,
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
    }}>

      {/* Current Progress */}
      {currentProgress && (
        <Box sx={{
          p: 3,
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          borderRadius: 2,
          border: '1px solid rgba(147, 51, 234, 0.2)'
        }}>
          <Typography variant="h6" sx={{ color: '#d8b4fe', mb: 2 }}>
            Currently Indexing
          </Typography>
          <Typography variant="body2" sx={{ color: '#94a3b8', mb: 2 }}>
            {currentProgress.file}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={currentProgress.progress}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'rgba(147, 51, 234, 0.2)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#9333ea',
                borderRadius: 4,
              }
            }}
          />
          <Typography variant="caption" sx={{ color: '#94a3b8', mt: 1, display: 'block' }}>
            {Math.round(currentProgress.progress)}% complete
          </Typography>
        </Box>
      )}

      {/* Activity Log */}
      <Box sx={{
        flex: 1,
        backgroundColor: 'rgba(15, 15, 15, 0.8)',
        borderRadius: 2,
        border: '1px solid rgba(147, 51, 234, 0.1)',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Typography variant="h6" sx={{
          color: '#d8b4fe',
          p: 2,
          borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
          backgroundColor: 'rgba(147, 51, 234, 0.05)'
        }}>
          Activity Log
        </Typography>

        <Box
          ref={logContainerRef}
          sx={{
            flex: 1,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(147, 51, 234, 0.1)',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(147, 51, 234, 0.3)',
              borderRadius: '4px',
            },
          }}
        >
          {logs.length === 0 ? (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#64748b'
            }}>
              <Typography>No indexing activity yet. Start indexing a project to see logs here.</Typography>
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {logs.map((log) => (
                <ListItem
                  key={log.id}
                  sx={{
                    borderBottom: '1px solid rgba(147, 51, 234, 0.05)',
                    '&:hover': {
                      backgroundColor: 'rgba(147, 51, 234, 0.05)'
                    }
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={log.type.toUpperCase()}
                          size="small"
                          sx={{
                            backgroundColor: log.type === 'error' ? 'rgba(239, 68, 68, 0.2)' : 'rgba(34, 197, 94, 0.2)',
                            color: log.type === 'error' ? '#ef4444' : '#22c55e',
                            fontWeight: 600,
                            fontSize: '0.7rem'
                          }}
                        />
                        <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                          {log.message}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Typography variant="caption" sx={{ color: '#64748b' }}>
                        {log.timestamp}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </Box>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\EmptyTabContent.tsx

/**
 * @file src/renderer/components/EmptyTabContent.tsx
 * @description Content displayed in empty tabs (tabs without projects)
 */

import React from 'react';
import { Box, Typography } from '@mui/material';
import { ProjectsPanel } from './ProjectsPanel';
import type { Project } from '../../shared/ipc.d.ts';

interface EmptyTabContentProps {
  onProjectSelect: (project: Project) => void;
  openProjects: (Project | null)[];
}

export const EmptyTabContent: React.FC<EmptyTabContentProps> = ({
  onProjectSelect,
  openProjects
}) => {
  const handleProjectSelect = (project: Project) => {
    console.log('EmptyTabContent.handleProjectSelect called with:', project);
    onProjectSelect(project);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f'
    }}>
      {/* Header */}
      <Box sx={{ 
        p: 3, 
        borderBottom: '1px solid #2a2a2a',
        backgroundColor: '#1a1a1a'
      }}>
        <Typography 
          variant="h5" 
          sx={{ 
            color: '#ffffff', 
            fontWeight: 600,
            mb: 1
          }}
        >
          Выберите проект
        </Typography>
        <Typography 
          variant="body2" 
          sx={{ 
            color: '#6b7280'
          }}
        >
          Выберите существующий проект или создайте новый для этой вкладки
        </Typography>
      </Box>

      {/* Projects Panel */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <ProjectsPanel
          onProjectSelect={handleProjectSelect}
          openProjects={openProjects.filter(Boolean) as Project[]}
          disableAutoSelection={true}
        />
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\FileExplorerPanel.tsx

/**
 * @file src/renderer/components/FileExplorerPanel.tsx
 * @description File explorer panel component for browsing project files
 */

import React from 'react';
import { Box, Typography } from '@mui/material';
import { FileTree } from './FileTree';

interface FileExplorerPanelProps {
  project: any | null;
  onSelectionChange: (selection: Set<string>) => void;
}

export const FileExplorerPanel: React.FC<FileExplorerPanelProps> = ({
  project,
  onSelectionChange
}) => {
  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Typography 
        variant="h5" 
        sx={{ 
          color: '#d8b4fe', 
          fontWeight: 600, 
          mb: 3,
          borderBottom: '2px solid rgba(147, 51, 234, 0.2)',
          pb: 2
        }}
      >
        File Explorer
      </Typography>
      
      {project ? (
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <Typography 
            variant="body2" 
            sx={{ 
              color: '#94a3b8', 
              mb: 2,
              fontFamily: 'monospace',
              backgroundColor: 'rgba(147, 51, 234, 0.1)',
              p: 1,
              borderRadius: 1
            }}
          >
            {project.path}
          </Typography>
          <FileTree
            project={project}
            onSelectionChange={onSelectionChange}
            compactMode="normal"
            showCheckboxes={true}
          />
        </Box>
      ) : (
        <Box sx={{ 
          flex: 1, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          textAlign: 'center'
        }}>
          <Typography variant="body1" sx={{ color: '#64748b' }}>
            No project selected. Please select a project from the Projects panel.
          </Typography>
        </Box>
      )}
    </Box>
  );
};


---

// FILE: src\renderer\components\FileTree.tsx

import React, { useState, useEffect, useCallback } from 'react';
import type { Project, FileTreeNode } from '../../shared/ipc.d';

interface FileTreeProps {
  project: Project | null;
  onSelectionChange: (selected: Set<string>) => void;
}

const GITIGNORE_PATTERNS = [
  'node_modules', 'dist', 'build', '.git', '.vscode', '__pycache__', 'target', 'bin', 'obj'
];

const shouldIgnore = (name: string): boolean => {
  return GITIGNORE_PATTERNS.some(pattern => name.toLowerCase().includes(pattern));
};

interface TreeItem extends FileTreeNode {
  level: number;
  expanded: boolean;
}

const TreeNode: React.FC<{
  item: TreeItem;
  selected: boolean;
  onToggle: () => void;
  onSelect: () => void;
}> = ({ item, selected, onToggle, onSelect }) => {
  const isDir = item.hasChildren;
  const paddingLeft = item.level * 20 + 8;

  return (
    <div
      className="flex items-center py-1 px-2 hover:bg-gray-800 cursor-pointer"
      style={{ paddingLeft }}
    >
      <input
        type="checkbox"
        checked={selected}
        onChange={onSelect}
        className="mr-2"
      />

      {isDir && (
        <button onClick={onToggle} className="mr-1 text-gray-400">
          {item.expanded ? '▼' : '▶'}
        </button>
      )}

      <span className="text-gray-200">{item.name}</span>
    </div>
  );
};

export const FileTree = ({ project, onSelectionChange }: FileTreeProps) => {
  const [nodes, setNodes] = useState<Record<string, FileTreeNode[]>>({});
  const [expanded, setExpanded] = useState<Set<string>>(new Set());
  const [selected, setSelected] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  // Flatten tree for rendering
  const flatItems: TreeItem[] = [];

  const flatten = (parentId: string, level: number) => {
    const children = nodes[parentId] || [];
    for (const node of children) {
      const item: TreeItem = {
        ...node,
        level,
        expanded: expanded.has(node.id)
      };
      flatItems.push(item);

      if (item.expanded && item.hasChildren) {
        flatten(node.id, level + 1);
      }
    }
  };

  if (nodes.root) {
    flatten('root', 0);
  }

  // Load project files
  useEffect(() => {
    if (!project?.path) {
      setNodes({});
      setExpanded(new Set());
      setSelected(new Set());
      return;
    }

    setLoading(true);
    window.electronAPI
      .readDirectoryLazy(project.path)
      .then((rootNodes) => {
        if (rootNodes) {
          setNodes({ root: rootNodes });
          // Auto-select files that are not ignored
          const autoSelected = new Set<string>();
          rootNodes.forEach(node => {
            if (!node.hasChildren && !shouldIgnore(node.name)) {
              autoSelected.add(node.id);
            }
          });
          setSelected(autoSelected);
        }
      })
      .catch(console.error)
      .finally(() => setLoading(false));
  }, [project?.path]);

  // Update selection callback
  useEffect(() => {
    onSelectionChange(selected);
  }, [selected, onSelectionChange]);

  // Toggle folder expansion
  const toggleExpanded = useCallback(async (nodeId: string) => {
    const newExpanded = new Set(expanded);

    if (expanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);

      // Load children if not loaded
      if (!nodes[nodeId]) {
        try {
          const children = await window.electronAPI.readDirectoryLazy(nodeId);
          if (children) {
            setNodes(prev => ({ ...prev, [nodeId]: children }));
          }
        } catch (error) {
          console.error('Failed to load children:', error);
          return;
        }
      }
    }

    setExpanded(newExpanded);
  }, [expanded, nodes]);

  // Toggle file selection
  const toggleSelected = useCallback((nodeId: string) => {
    const newSelected = new Set(selected);
    if (selected.has(nodeId)) {
      newSelected.delete(nodeId);
    } else {
      newSelected.add(nodeId);
    }
    setSelected(newSelected);
  }, [selected]);

  // Control buttons
  const handleAutoSelect = useCallback(async () => {
    if (!project?.path) return;

    const autoSelected = new Set<string>();

    const processNodes = (nodeId: string) => {
      const children = nodes[nodeId] || [];
      for (const child of children) {
        if (!child.hasChildren && !shouldIgnore(child.name)) {
          autoSelected.add(child.id);
        } else if (child.hasChildren && !shouldIgnore(child.name)) {
          processNodes(child.id);
        }
      }
    };

    processNodes('root');
    setSelected(autoSelected);
  }, [project?.path, nodes]);

  const handleSelectAll = useCallback(() => {
    const allFiles = new Set<string>();

    const processNodes = (nodeId: string) => {
      const children = nodes[nodeId] || [];
      for (const child of children) {
        if (!child.hasChildren) {
          allFiles.add(child.id);
        } else if (child.hasChildren) {
          processNodes(child.id);
        }
      }
    };

    processNodes('root');
    setSelected(allFiles);
  }, [nodes]);

  const handleUnselectAll = useCallback(() => {
    setSelected(new Set());
  }, []);

  if (loading) {
    return (
      <div className="p-4">
        <div className="text-gray-400">Loading...</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-4">
        <div className="text-gray-400">No project selected</div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Control buttons */}
      <div className="p-2 border-b border-gray-700">
        <div className="flex gap-1">
          <button
            onClick={handleAutoSelect}
            className="flex-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 rounded"
          >
            Auto Select
          </button>
          <button
            onClick={handleSelectAll}
            className="flex-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 rounded"
          >
            Select All
          </button>
          <button
            onClick={handleUnselectAll}
            className="flex-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 rounded"
          >
            Unselect All
          </button>
        </div>
      </div>

      {/* File tree */}
      <div className="flex-1 overflow-y-auto">
        {flatItems.map((item) => (
          <TreeNode
            key={item.id}
            item={item}
            selected={selected.has(item.id)}
            onToggle={() => toggleExpanded(item.id)}
            onSelect={() => toggleSelected(item.id)}
          />
        ))}
      </div>
    </div>
  );
};


---

// FILE: src\renderer\components\ProjectExplorer.tsx

/**
 * @file src/renderer/components/ProjectExplorer.tsx
 * @description Ultra-optimized project manager with Tailwind v4, GPU acceleration, and smooth animations
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { Project } from '../../shared/ipc.d';
import '../styles/globals.css';

// Optimized SVG icons for maximum performance
const ICONS = {
  add: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>`,
  delete: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>`,
  folder: `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
  folderOpen: `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
  storage: `<svg class="size-12" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z"></path></svg>`,
} as const;

interface ProjectExplorerProps {
  onProjectSelect: (project: Project | null) => void;
  openProjects?: Project[];
  disableAutoSelection?: boolean;
}

export const ProjectExplorer = ({ onProjectSelect, openProjects = [], disableAutoSelection = false }: ProjectExplorerProps) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [hoveredProjectId, setHoveredProjectId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasManualSelection, setHasManualSelection] = useState(false);

  // Optimized project fetching with error handling
  const fetchProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      const projs = await window.electronAPI.getAllProjects();
      setProjects(projs);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Separate effect for initial project selection
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // Handle initial project selection separately
  useEffect(() => {
    console.log('Project selection effect triggered:', {
      projectsLength: projects.length,
      selectedProjectId,
      hasManualSelection,
      projects: projects.map(p => ({ id: p.id, name: p.name }))
    });

    if (projects.length > 0 && selectedProjectId === null && !hasManualSelection && !disableAutoSelection) {
      console.log('Auto-selecting first project');
      const firstProject = projects[0];
      setSelectedProjectId(firstProject.id);
      onProjectSelect(firstProject);
    } else if (projects.length === 0 && selectedProjectId !== null) {
      console.log('Clearing selection - no projects');
      setSelectedProjectId(null);
      onProjectSelect(null);
      setHasManualSelection(false);
    }
  }, [projects, selectedProjectId, onProjectSelect, hasManualSelection]);

  // Optimized event handlers with loading states
  const handleAddProject = useCallback(async (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    try {
      setIsLoading(true);
      console.log('Opening directory dialog...');
      const directoryPath = await window.electronAPI.openDirectoryDialog();
      console.log('Directory selected:', directoryPath);

      if (directoryPath) {
        console.log('Adding project...');
        const newProject = await window.electronAPI.addProject(directoryPath);
        console.log('Project added:', newProject);
        await fetchProjects();

        // Автоматически выбираем новый добавленный проект
        if (newProject) {
          setSelectedProjectId(newProject.id);
          setHasManualSelection(true);
          onProjectSelect(newProject);
        }
      } else {
        console.log('No directory selected');
      }
    } catch (error) {
      console.error('Failed to add project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects, onProjectSelect]);

  const handleDeleteProject = useCallback(async (projectId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      setIsLoading(true);
      await window.electronAPI.deleteProject(projectId);
      await fetchProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects]);

  const handleSelectProject = useCallback((project: Project) => {
    console.log('handleSelectProject called with:', project);
    setSelectedProjectId(project.id);
    setHasManualSelection(true);
    onProjectSelect(project);
  }, [onProjectSelect]);

  // Memoized project list for better performance
  const projectList = useMemo(() => projects, [projects]);

  return (
    <div className="p-4 h-full flex flex-col gpu-layer">
      {/* Add button */}
      <div className="flex justify-end items-center mb-6 pb-3 border-b border-primary-500/20">
        <button
          onClick={handleAddProject}
          disabled={isLoading}
          className="
            group relative flex items-center gap-2 px-4 py-2
            bg-gradient-to-r from-primary-600 to-primary-500
            hover:from-primary-700 hover:to-primary-600
            disabled:from-gray-600 disabled:to-gray-500
            text-white text-sm font-medium rounded-lg
            transition-all duration-200 ease-out
            shadow-lg shadow-primary-500/25 hover:shadow-primary-500/40
            transform hover:scale-105 active:scale-95
            focus-ring gpu-layer
          "
          title="Add new project"
        >
          <div
            className={`transition-transform duration-200 ${isLoading ? 'animate-spin' : 'group-hover:scale-110'}`}
            dangerouslySetInnerHTML={{ __html: ICONS.add }}
          />
          <span>Add</span>

          {/* Subtle glow effect */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary-600 to-primary-500 opacity-0 group-hover:opacity-20 transition-opacity duration-200 blur-sm -z-10" />
        </button>
      </div>

      {/* Project list with optimized scrolling */}
      <div className="flex-1 overflow-y-auto scrollbar-ultra-smooth">
        {projectList.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8 animate-fade-in gpu-layer">
            <div
              className="mb-4 text-gray-600 opacity-60"
              dangerouslySetInnerHTML={{ __html: ICONS.storage }}
            />
            <h3 className="text-base font-medium text-gray-400 mb-1">
              No projects yet
            </h3>
            <p className="text-sm text-gray-500">
              Click "Add" to get started
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {projectList.map((project, index) => (
              <div
                key={project.id}
                className="
                  group relative animate-slide-in gpu-layer
                  transition-all duration-200 ease-out
                  hover:transform hover:scale-[1.02] hover:-translate-y-0.5
                "
                style={{ animationDelay: `${index * 50}ms` }}
                onMouseEnter={() => setHoveredProjectId(project.id)}
                onMouseLeave={() => setHoveredProjectId(null)}
              >
                <button
                  onClick={() => handleSelectProject(project)}
                  className={`
                    w-full text-left p-4 rounded-xl border transition-all duration-200 ease-out
                    backdrop-blur-sm focus-ring gpu-layer relative overflow-hidden
                    ${selectedProjectId === project.id
                      ? 'bg-gradient-to-br from-primary-500/20 to-primary-600/10 border-primary-500/40 shadow-lg shadow-primary-500/20'
                      : 'bg-gray-800/40 border-gray-700/30 hover:bg-gray-800/60 hover:border-gray-600/50'
                    }
                  `}
                >
                  <div className="flex items-center gap-3 relative z-10">
                    {/* Project Icon */}
                    <div
                      className={`
                        transition-all duration-200 ease-out
                        ${selectedProjectId === project.id ? 'text-primary-300 scale-110' : 'text-gray-400'}
                      `}
                      dangerouslySetInnerHTML={{
                        __html: selectedProjectId === project.id ? ICONS.folderOpen : ICONS.folder
                      }}
                    />

                    {/* Project Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className={`
                        text-sm font-medium mb-1 transition-colors duration-200
                        ${selectedProjectId === project.id ? 'text-primary-200' : 'text-gray-200'}
                      `}>
                        {project.name}
                      </h3>

                      <p className="text-xs text-gray-500 font-mono truncate mb-2">
                        {project.path}
                      </p>

                      <div className="flex items-center gap-2">
                        {(() => {
                          const isOpen = openProjects.some(openProject => openProject?.path === project.path);
                          return (
                            <span className={`
                              inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                              transition-colors duration-200
                              ${isOpen
                                ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                                : selectedProjectId === project.id
                                  ? 'bg-primary-500/20 text-primary-300 border border-primary-500/30'
                                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'
                              }
                            `}>
                              {isOpen ? 'Open' : 'Ready'}
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>

                  {/* Subtle background glow for selected project */}
                  {selectedProjectId === project.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-primary-600/5 rounded-xl opacity-50" />
                  )}
                </button>
                {/* Delete button with smooth animations - исправлен размер */}
                <button
                  onClick={(e) => handleDeleteProject(project.id, e)}
                  disabled={isLoading}
                  className={`
                    absolute top-2 right-2 p-1.5 rounded-md
                    transition-all duration-200 ease-out z-20
                    hover:bg-red-500/20 hover:scale-110 active:scale-95
                    focus-ring disabled:opacity-50 disabled:cursor-not-allowed
                    ${hoveredProjectId === project.id
                      ? 'opacity-100 text-red-400'
                      : 'opacity-0 text-transparent pointer-events-none'
                    }
                  `}
                  title="Delete project"
                >
                  <div dangerouslySetInnerHTML={{ __html: ICONS.delete }} />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};


---

// FILE: src\renderer\components\ProjectTabBar.tsx

/**
 * @file src/renderer/components/ProjectTabBar.tsx
 * @description Tab bar component for managing multiple project tabs
 */

import React, { useState, useRef, useEffect } from 'react';
import { 
  Box, 
  Tab, 
  Tabs, 
  IconButton, 
  Tooltip, 
  Typography,
  Menu,
  MenuItem,
  alpha
} from '@mui/material';
import { 
  Add as AddIcon, 
  Close as CloseIcon, 
  MoreHoriz as MoreIcon,
  FolderOpen as FolderIcon
} from '@mui/icons-material';

interface ProjectTab {
  id: string;
  project: any | null;
  name: string;
  isActive: boolean;
  selectedFiles: Set<string>;
}

interface ProjectTabBarProps {
  tabs: ProjectTab[];
  activeTabId: string | null;
  onTabSwitch: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onNewTab: () => void;
  onProjectSelect: (tabId: string) => void;
}

export const ProjectTabBar: React.FC<ProjectTabBarProps> = ({
  tabs,
  activeTabId,
  onTabSwitch,
  onTabClose,
  onNewTab,
  onProjectSelect
}) => {
  const [overflowMenuAnchor, setOverflowMenuAnchor] = useState<null | HTMLElement>(null);
  const [draggedTab, setDraggedTab] = useState<string | null>(null);
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showOverflow, setShowOverflow] = useState(false);

  // Check if tabs overflow the container
  useEffect(() => {
    const checkOverflow = () => {
      if (tabsContainerRef.current) {
        const container = tabsContainerRef.current;
        const hasOverflow = container.scrollWidth > container.clientWidth;
        setShowOverflow(prev => {
          // Only update if the value actually changed to prevent infinite re-renders
          if (prev !== hasOverflow) {
            return hasOverflow;
          }
          return prev;
        });
      }
    };

    // Use a timeout to ensure DOM is ready
    const timeoutId = setTimeout(checkOverflow, 0);

    // Use ResizeObserver for better performance if available
    let resizeObserver: ResizeObserver | null = null;
    if (tabsContainerRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(checkOverflow);
      resizeObserver.observe(tabsContainerRef.current);
    } else {
      // Fallback to window resize event
      window.addEventListener('resize', checkOverflow);
    }

    return () => {
      clearTimeout(timeoutId);
      if (resizeObserver) {
        resizeObserver.disconnect();
      } else {
        window.removeEventListener('resize', checkOverflow);
      }
    };
  }, [tabs.length]); // Only depend on tabs.length instead of the entire tabs array

  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose(tabId);
  };

  const handleTabClick = (tabId: string) => {
    onTabSwitch(tabId);
  };

  const handleOverflowMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setOverflowMenuAnchor(event.currentTarget);
  };

  const handleOverflowMenuClose = () => {
    setOverflowMenuAnchor(null);
  };

  const truncateTabName = (name: string | undefined, maxLength: number = 20) => {
    if (!name) return 'Untitled';
    return name.length > maxLength ? `${name.substring(0, maxLength)}...` : name;
  };

  return (
    <Box
      role="tablist"
      aria-label="Project tabs"
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: '#1a1a1a',
        borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
        height: 50,
        minHeight: 50,
        maxHeight: 50,
        overflow: 'hidden'
      }}
    >
      {/* Tabs Container */}
      <Box
        ref={tabsContainerRef}
        sx={{
          flex: 1,
          display: 'flex',
          overflowX: 'auto',
          overflowY: 'hidden',
          scrollbarWidth: 'none',
          '&::-webkit-scrollbar': { display: 'none' }
        }}
      >
        {tabs.map((tab, index) => (
          <Box
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleTabClick(tab.id);
              }
            }}
            tabIndex={0}
            role="tab"
            aria-selected={tab.id === activeTabId}
            aria-label={`Project tab: ${tab.name}${tab.project ? ` (${tab.project.path})` : ''}`}
            sx={{
              display: 'flex',
              alignItems: 'center',
              minWidth: 120,
              maxWidth: 200,
              height: 50,
              px: 2,
              cursor: 'pointer',
              backgroundColor: tab.id === activeTabId
                ? alpha('#9333ea', 0.2)
                : 'transparent',
              borderRight: '1px solid rgba(147, 51, 234, 0.1)',
              borderTop: tab.id === activeTabId
                ? '2px solid #9333ea'
                : '2px solid transparent',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: alpha('#9333ea', 0.1),
              },
              '&:focus': {
                outline: '2px solid #9333ea',
                outlineOffset: '-2px',
              },
              position: 'relative'
            }}
          >
            {/* Tab Icon */}
            <FolderIcon 
              sx={{ 
                fontSize: 16, 
                mr: 1, 
                color: tab.project ? '#9333ea' : '#64748b',
                opacity: 0.8
              }} 
            />
            
            {/* Tab Name */}
            <Typography
              variant="body2"
              sx={{
                flex: 1,
                color: tab.id === activeTabId ? '#d8b4fe' : '#94a3b8',
                fontWeight: tab.id === activeTabId ? 600 : 400,
                fontSize: '0.875rem',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {truncateTabName(tab.name)}
            </Typography>

            {/* Close Button */}
            {tabs.length > 1 && (
              <IconButton
                size="small"
                onClick={(e) => handleTabClose(e, tab.id)}
                aria-label={`Close tab: ${tab.name}`}
                sx={{
                  ml: 1,
                  p: 0.5,
                  color: '#64748b',
                  opacity: 0.6,
                  '&:hover': {
                    opacity: 1,
                    color: '#f87171',
                    backgroundColor: alpha('#f87171', 0.1)
                  },
                  '&:focus': {
                    outline: '2px solid #f87171',
                    outlineOffset: '2px',
                  }
                }}
              >
                <CloseIcon sx={{ fontSize: 14 }} />
              </IconButton>
            )}
          </Box>
        ))}
      </Box>

      {/* Overflow Menu Button */}
      {showOverflow && (
        <IconButton
          onClick={handleOverflowMenuOpen}
          sx={{
            color: '#94a3b8',
            '&:hover': { color: '#d8b4fe' }
          }}
        >
          <MoreIcon />
        </IconButton>
      )}

      {/* New Tab Button */}
      <Tooltip title="New Tab (Ctrl+T)">
        <IconButton
          onClick={onNewTab}
          sx={{
            mx: 1,
            color: '#94a3b8',
            '&:hover': {
              color: '#d8b4fe',
              backgroundColor: alpha('#9333ea', 0.1)
            }
          }}
        >
          <AddIcon />
        </IconButton>
      </Tooltip>

      {/* Overflow Menu */}
      <Menu
        anchorEl={overflowMenuAnchor}
        open={Boolean(overflowMenuAnchor)}
        onClose={handleOverflowMenuClose}
        PaperProps={{
          sx: {
            backgroundColor: '#1a1a1a',
            border: '1px solid rgba(147, 51, 234, 0.2)',
            '& .MuiMenuItem-root': {
              color: '#94a3b8',
              '&:hover': {
                backgroundColor: alpha('#9333ea', 0.1),
                color: '#d8b4fe'
              }
            }
          }
        }}
      >
        {tabs.map((tab) => (
          <MenuItem
            key={tab.id}
            onClick={() => {
              handleTabClick(tab.id);
              handleOverflowMenuClose();
            }}
            selected={tab.id === activeTabId}
          >
            <FolderIcon sx={{ fontSize: 16, mr: 1 }} />
            {tab.name}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};


---

// FILE: src\renderer\components\ProjectsPanel.tsx

/**
 * @file src/renderer/components/ProjectsPanel.tsx
 * @description Projects panel component for managing and selecting projects
 */

import React from 'react';
import { Box, Typography } from '@mui/material';
import { ProjectExplorer } from './ProjectExplorer';
import { CompactProjectExplorer } from './CompactProjectExplorer';

interface ProjectsPanelProps {
  onProjectSelect: (project: any) => void;
  openProjects?: any[]; // Список уже открытых проектов
  disableAutoSelection?: boolean;
  compact?: boolean; // Использовать компактную версию
}

export const ProjectsPanel: React.FC<ProjectsPanelProps> = ({
  onProjectSelect,
  openProjects = [],
  disableAutoSelection = false,
  compact = false
}) => {
  const handleProjectSelect = (project: any) => {
    console.log('ProjectsPanel.handleProjectSelect called with:', project);
    onProjectSelect(project);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {compact ? (
          <CompactProjectExplorer
            onProjectSelect={handleProjectSelect}
            openProjects={openProjects}
            disableAutoSelection={disableAutoSelection}
          />
        ) : (
          <ProjectExplorer
            onProjectSelect={handleProjectSelect}
            openProjects={openProjects}
            disableAutoSelection={disableAutoSelection}
          />
        )}
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\QueryInterface.tsx

/**
 * @file src/renderer/components/QueryInterface.tsx
 * @description React component for manually testing the MCP API.
 */

import React, { useState } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Paper,
  TextField,
  Typography,
} from '@mui/material';

export const QueryInterface = () => {
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleQuery = async () => {
    if (!query.trim()) return;
    setIsLoading(true);
    setResponse('');
    try {
      // This simulates a direct call to the local MCP API
      const res = await fetch('http://127.0.0.1:4888/mcp/v1/context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question: query }),
      });
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const data = await res.json();
      setResponse(JSON.stringify(data, null, 2));
    } catch (error: any) {
      setResponse(`Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Manual API Test
      </Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <TextField
          label="Enter your query"
          variant="outlined"
          fullWidth
          multiline
          rows={3}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            onClick={handleQuery}
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {isLoading ? 'Querying...' : 'Send Query'}
          </Button>
        </Box>
        {response && (
          <Paper
            variant="outlined"
            sx={{ p: 2, mt: 2, backgroundColor: 'grey.100', maxHeight: 300, overflowY: 'auto' }}
          >
            <Typography variant="subtitle2" gutterBottom>Response:</Typography>
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
              {response}
            </pre>
          </Paper>
        )}
      </Box>
    </Paper>
  );
};


---

// FILE: src\renderer\components\SettingsPanel.tsx

/**
 * @file src/renderer/components/SettingsPanel.tsx
 * @description Settings panel component for application configuration
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Card,
  CardContent
} from '@mui/material';
import { RestartAlt, Warning } from '@mui/icons-material';

export const SettingsPanel: React.FC = () => {
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const handleFactoryReset = async () => {
    setIsResetting(true);

    try {
      // Очищаем localStorage
      localStorage.clear();
      console.log('Cleared localStorage');

      // Очищаем данные Electron через IPC
      if (window.electronAPI?.clearAppData) {
        await window.electronAPI.clearAppData();
        console.log('Cleared Electron app data');
      }

      // Показываем уведомление и перезагружаем приложение
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Error during factory reset:', error);
    } finally {
      setIsResetting(false);
      setResetDialogOpen(false);
    }
  };

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Раздел сброса данных */}
        <Card sx={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: 2
        }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Warning sx={{ color: '#ef4444' }} />
              <Typography variant="h6" sx={{ color: '#ef4444', fontWeight: 600 }}>
                Danger Zone
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ color: '#94a3b8', mb: 3 }}>
              Reset all application data to factory defaults. This will remove all projects,
              tabs, settings, and cached data. This action cannot be undone.
            </Typography>

            <Button
              variant="outlined"
              color="error"
              startIcon={<RestartAlt />}
              onClick={() => setResetDialogOpen(true)}
              sx={{
                borderColor: '#ef4444',
                color: '#ef4444',
                '&:hover': {
                  borderColor: '#dc2626',
                  backgroundColor: 'rgba(239, 68, 68, 0.1)'
                }
              }}
            >
              Factory Reset
            </Button>
          </CardContent>
        </Card>

        {/* Placeholder для других настроек */}
        <Card sx={{
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          border: '1px solid rgba(147, 51, 234, 0.3)',
          borderRadius: 2
        }}>
          <CardContent>
            <Typography variant="h6" sx={{ color: '#d8b4fe', fontWeight: 600, mb: 2 }}>
              General Settings
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b' }}>
              Additional application settings will be implemented here
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Диалог подтверждения сброса */}
      <Dialog
        open={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: '#1a1a1a',
            border: '1px solid rgba(239, 68, 68, 0.3)'
          }
        }}
      >
        <DialogTitle sx={{ color: '#ef4444', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Warning />
          Factory Reset Confirmation
        </DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            This action will permanently delete all application data!
          </Alert>
          <Typography sx={{ color: '#94a3b8' }}>
            Are you sure you want to reset SmartRAG to factory defaults? This will:
          </Typography>
          <Box component="ul" sx={{ color: '#94a3b8', mt: 1, pl: 2 }}>
            <li>Remove all project tabs and data</li>
            <li>Clear all cached files and indexes</li>
            <li>Reset all application settings</li>
            <li>Clear localStorage and Electron data</li>
          </Box>
          <Typography sx={{ color: '#ef4444', mt: 2, fontWeight: 600 }}>
            This action cannot be undone!
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setResetDialogOpen(false)}
            sx={{ color: '#94a3b8' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleFactoryReset}
            color="error"
            variant="contained"
            disabled={isResetting}
            startIcon={<RestartAlt />}
          >
            {isResetting ? 'Resetting...' : 'Reset to Factory Defaults'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};


---

// FILE: src\renderer\components\Sidebar.tsx

/**
 * @file src/renderer/components/Sidebar.tsx
 * @description Left sidebar navigation component with collapsible sections
 */

import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Typography,
  alpha
} from '@mui/material';
import {
  FolderOpen as FilesIcon,
  Folder as ProjectsIcon,
  Dashboard as DashboardIcon,
  Chat as ChatIcon,
  Settings as SettingsIcon,
  ChevronLeft as CollapseIcon,
  ChevronRight as ExpandIcon
} from '@mui/icons-material';

type SidebarSection = 'files' | 'projects' | 'dashboard' | 'chat' | 'settings';

interface SidebarProps {
  sidebarSection: SidebarSection;
  sidebarCollapsed: boolean;
  onSectionChange: (section: SidebarSection) => void;
  onSidebarToggle: () => void;
}

const sidebarSections = [
  { id: 'projects' as SidebarSection, label: 'Projects', icon: ProjectsIcon },
  { id: 'files' as SidebarSection, label: 'File Explorer', icon: FilesIcon },
  { id: 'dashboard' as SidebarSection, label: 'Dashboard', icon: DashboardIcon },
  { id: 'chat' as SidebarSection, label: 'Chat', icon: ChatIcon },
  { id: 'settings' as SidebarSection, label: 'Settings', icon: SettingsIcon },
];

export const Sidebar: React.FC<SidebarProps> = ({
  sidebarSection,
  sidebarCollapsed,
  onSectionChange,
  onSidebarToggle
}) => {
  const handleSectionClick = (section: SidebarSection) => {
    onSectionChange(section);
  };



  const sidebarWidth = sidebarCollapsed ? 60 : 180;

  return (
    <Box
      role="navigation"
      aria-label="Main navigation sidebar"
      sx={{
        width: sidebarWidth,
        height: '100vh',
        backgroundColor: '#1a1a1a',
        borderRight: '1px solid rgba(147, 51, 234, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        transition: 'width 0.3s ease',
        overflow: 'hidden'
      }}
    >
      {/* Sidebar Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: sidebarCollapsed ? 'center' : 'space-between',
          px: 2,
          py: 1,
          borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
          height: 50,
          minHeight: 50,
          maxHeight: 50
        }}
      >
        {!sidebarCollapsed && (
          <Typography
            variant="h6"
            sx={{
              color: '#d8b4fe',
              fontWeight: 600,
              fontSize: '1.1rem'
            }}
          >
            SmartRAG
          </Typography>
        )}
        
        <Tooltip title={sidebarCollapsed ? 'Expand Sidebar (Ctrl+B)' : 'Collapse Sidebar (Ctrl+B)'}>
          <IconButton
            onClick={onSidebarToggle}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            sx={{
              color: '#94a3b8',
              '&:hover': {
                color: '#d8b4fe',
                backgroundColor: alpha('#9333ea', 0.1)
              },
              '&:focus': {
                outline: '2px solid #9333ea',
                outlineOffset: '2px',
              }
            }}
          >
            {sidebarCollapsed ? <ExpandIcon /> : <CollapseIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {/* Navigation Sections */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <List sx={{ p: 0 }}>
          {sidebarSections.map((section) => {
            const isActive = sidebarSection === section.id;
            const IconComponent = section.icon;

            return (
              <ListItem key={section.id} disablePadding>
                <ListItemButton
                  onClick={() => handleSectionClick(section.id)}
                  aria-label={`Navigate to ${section.label} section`}
                  aria-current={isActive ? 'page' : undefined}
                  sx={{
                    px: sidebarCollapsed ? 1 : 2,
                    py: 0,
                    height: 50,
                    minHeight: 50,
                    maxHeight: 50,
                    backgroundColor: isActive ? alpha('#9333ea', 0.2) : 'transparent',
                    borderLeft: isActive ? '3px solid #9333ea' : '3px solid transparent',
                    '&:hover': {
                      backgroundColor: alpha('#9333ea', 0.1)
                    },
                    '&:focus': {
                      outline: '2px solid #9333ea',
                      outlineOffset: '-2px',
                    }
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: sidebarCollapsed ? 'auto' : 40,
                      color: isActive ? '#d8b4fe' : '#94a3b8',
                      justifyContent: 'center'
                    }}
                  >
                    <IconComponent />
                  </ListItemIcon>

                  {!sidebarCollapsed && (
                    <ListItemText
                      primary={section.label}
                      primaryTypographyProps={{
                        sx: {
                          color: isActive ? '#d8b4fe' : '#94a3b8',
                          fontWeight: isActive ? 600 : 400,
                          fontSize: '0.9rem'
                        }
                      }}
                    />
                  )}
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\main.tsx

/**
 * @file src/renderer/main.tsx
 * @description Main entry point for the React application in the renderer process.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
// import TestApp from './TestApp.tsx';
import './index.css';

console.log('[main.tsx] Starting React application...');
console.log('[main.tsx] Document ready state:', document.readyState);
console.log('[main.tsx] Window object:', window);
console.log('[main.tsx] ElectronAPI available:', !!window.electronAPI);

const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('[main.tsx] Root element not found!');
  document.body.innerHTML = '<div style="color: red; font-size: 20px; padding: 20px;">ERROR: Root element not found!</div>';
} else {
  console.log('[main.tsx] Root element found, creating React root...');
  console.log('[main.tsx] Root element:', rootElement);
  
  try {
    const root = ReactDOM.createRoot(rootElement);
    console.log('[main.tsx] React root created successfully');
    
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
    console.log('[main.tsx] React app rendered successfully!');
    
    // Добавим проверку через небольшую задержку
    setTimeout(() => {
      console.log('[main.tsx] Root element content after render:', rootElement.innerHTML.substring(0, 200));
    }, 1000);
    
  } catch (error) {
    console.error('[main.tsx] Error rendering React app:', error);
    console.error('[main.tsx] Error stack:', error instanceof Error ? error.stack : 'Unknown error');
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    document.body.innerHTML = `<div style="color: red; font-size: 16px; padding: 20px;">RENDER ERROR: ${errorMessage}</div>`;
  }
}


---

// FILE: src\renderer\preload.ts

/**
 * @file src/renderer/preload.ts
 * @description Preload script for the renderer process.
 * Exposes Node.js APIs to the renderer process in a secure way using contextBridge.
 */

import { contextBridge, ipcRenderer } from 'electron';
import type { IpcApi, IndexingUpdatePayload } from '../shared/ipc.d';

const ipcApi: IpcApi = {
  // Indexing
  startIndexing: (payload: { filePath: string; projectId: number }) => {
    ipcRenderer.send('start-indexing', payload);
  },
  onIndexingUpdate: (callback: (payload: IndexingUpdatePayload) => void) => {
    const listener = (event: any, payload: IndexingUpdatePayload) => callback(payload);
    ipcRenderer.on('indexing-update', listener);
    return () => {
      ipcRenderer.removeListener('indexing-update', listener);
    };
  },

  // Projects
  getAllProjects: () => ipcRenderer.invoke('projects:getAll'),
  addProject: (path: string) => ipcRenderer.invoke('projects:add', path),
  deleteProject: (id: number) => ipcRenderer.invoke('projects:delete', id),

  // FS
  openDirectoryDialog: () => ipcRenderer.invoke('dialog:openDirectory'),
  readDirectory: (path: string) => ipcRenderer.invoke('fs:readDirectory', path),
  readDirectoryLazy: (path: string) => ipcRenderer.invoke('fs:readDirectoryLazy', path),

  // App Data
  clearAppData: () => ipcRenderer.invoke('app:clearData'),
};

contextBridge.exposeInMainWorld('electronAPI', ipcApi);


---

// FILE: tsconfig.json

{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowImportingTsExtensions": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src", "electron"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
